const express = require('express');
const router = express.Router();
const logger = require('../utils/logger');

/**
 * API routes for email processing and management
 */
function createApiRoutes(emailService, emailProcessor, sheetsService) {

  /**
   * GET /api/emails/check
   * Manually trigger email check
   */
  router.get('/emails/check', async (req, res) => {
    try {
      logger.info('Manual email check triggered');

      const emails = await emailService.fetchNewEmails();
      const results = await emailProcessor.processBatch(emails);

      res.json({
        success: true,
        message: `Processed ${results.processed} emails`,
        results
      });
    } catch (error) {
      logger.error('Error in manual email check:', error);
      res.status(500).json({
        error: 'Failed to check emails',
        details: error.message
      });
    }
  });

  /**
   * GET /api/stats
   * Get processing statistics
   */
  router.get('/stats', async (req, res) => {
    try {
      const stats = await emailProcessor.getProcessingStats();

      res.json({
        success: true,
        stats
      });
    } catch (error) {
      logger.error('Error getting stats:', error);
      res.status(500).json({
        error: 'Failed to get statistics',
        details: error.message
      });
    }
  });

  /**
   * GET /api/sheets/data
   * Get all data from Google Sheets
   */
  router.get('/sheets/data', async (req, res) => {
    try {
      const data = await sheetsService.getAllData();

      res.json({
        success: true,
        data,
        count: data.length > 0 ? data.length - 1 : 0 // Exclude header row
      });
    } catch (error) {
      logger.error('Error getting sheets data:', error);
      res.status(500).json({
        error: 'Failed to get sheets data',
        details: error.message
      });
    }
  });

  /**
   * POST /api/sheets/clear
   * Clear all data from Google Sheets
   */
  router.post('/sheets/clear', async (req, res) => {
    try {
      await sheetsService.clearData();

      res.json({
        success: true,
        message: 'Sheets data cleared successfully'
      });
    } catch (error) {
      logger.error('Error clearing sheets data:', error);
      res.status(500).json({
        error: 'Failed to clear sheets data',
        details: error.message
      });
    }
  });

  /**
   * POST /api/columns/update
   * Update column headers for processing
   */
  router.post('/columns/update', async (req, res) => {
    try {
      const { columns } = req.body;

      if (!columns || !Array.isArray(columns)) {
        return res.status(400).json({
          error: 'Invalid columns data. Expected array of column names.'
        });
      }

      emailProcessor.setColumnHeaders(columns);

      res.json({
        success: true,
        message: 'Column headers updated successfully',
        columns
      });
    } catch (error) {
      logger.error('Error updating columns:', error);
      res.status(500).json({
        error: 'Failed to update columns',
        details: error.message
      });
    }
  });

  /**
   * GET /api/columns
   * Get current column headers
   */
  router.get('/columns', (req, res) => {
    try {
      const columns = emailProcessor.getColumnHeaders();

      res.json({
        success: true,
        columns
      });
    } catch (error) {
      logger.error('Error getting columns:', error);
      res.status(500).json({
        error: 'Failed to get columns',
        details: error.message
      });
    }
  });

  /**
   * POST /api/emails/process-custom
   * Process emails with custom column extraction
   */
  router.post('/emails/process-custom', async (req, res) => {
    try {
      const { columns } = req.body;

      if (!columns || !Array.isArray(columns)) {
        return res.status(400).json({
          error: 'Invalid columns data. Expected array of column names.'
        });
      }

      const emails = await emailService.fetchNewEmails();
      const results = await emailProcessor.processEmailsWithCustomExtraction(emails, columns);

      res.json({
        success: true,
        message: `Processed ${results.processed} emails with custom extraction`,
        results
      });
    } catch (error) {
      logger.error('Error in custom email processing:', error);
      res.status(500).json({
        error: 'Failed to process emails with custom extraction',
        details: error.message
      });
    }
  });

  /**
   * GET /api/spreadsheets
   * Get available spreadsheets
   */
  router.get('/spreadsheets', async (req, res) => {
    try {
      const spreadsheets = await sheetsService.getAvailableSpreadsheets();

      res.json({
        success: true,
        spreadsheets
      });
    } catch (error) {
      logger.error('Error getting spreadsheets:', error);
      res.status(500).json({
        error: 'Failed to get spreadsheets',
        details: error.message
      });
    }
  });

  /**
   * GET /api/spreadsheets/:id/sheets
   * Get sheets in a spreadsheet
   */
  router.get('/spreadsheets/:id/sheets', async (req, res) => {
    try {
      const { id } = req.params;
      const sheets = await sheetsService.getAvailableSheets(id);

      res.json({
        success: true,
        sheets
      });
    } catch (error) {
      logger.error('Error getting sheets:', error);
      res.status(500).json({
        error: 'Failed to get sheets',
        details: error.message
      });
    }
  });

  /**
   * POST /api/config/sheet
   * Set active spreadsheet and sheet
   */
  router.post('/config/sheet', async (req, res) => {
    try {
      const { spreadsheetId, sheetName } = req.body;

      if (!spreadsheetId) {
        return res.status(400).json({
          error: 'Spreadsheet ID is required'
        });
      }

      sheetsService.setSpreadsheetId(spreadsheetId);

      if (sheetName) {
        sheetsService.setSheetName(sheetName);
      }

      res.json({
        success: true,
        message: 'Sheet configuration updated',
        config: {
          spreadsheetId,
          sheetName: sheetName || sheetsService.sheetName
        }
      });
    } catch (error) {
      logger.error('Error setting sheet config:', error);
      res.status(500).json({
        error: 'Failed to set sheet configuration',
        details: error.message
      });
    }
  });

  /**
   * GET /api/config/sheet
   * Get current sheet configuration
   */
  router.get('/config/sheet', (req, res) => {
    try {
      res.json({
        success: true,
        config: {
          spreadsheetId: sheetsService.spreadsheetId,
          sheetName: sheetsService.sheetName
        }
      });
    } catch (error) {
      logger.error('Error getting sheet config:', error);
      res.status(500).json({
        error: 'Failed to get sheet configuration',
        details: error.message
      });
    }
  });

  /**
   * GET /api/health
   * Health check endpoint
   */
  router.get('/health', (req, res) => {
    res.json({
      success: true,
      message: 'Praatika agent is running',
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    });
  });

  return router;
}

module.exports = createApiRoutes;
