require('dotenv').config();

const config = {
  server: {
    port: process.env.PORT || 3000,
    env: process.env.NODE_ENV || 'development'
  },

  google: {
    clientId: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    apiKey: process.env.GOOGLE_API_KEY,
    redirectUri: process.env.GOOGLE_REDIRECT_URI || 'http://localhost:3000/auth/google/callback',
    scopes: [
      'https://www.googleapis.com/auth/gmail.readonly',
      'https://www.googleapis.com/auth/gmail.modify',
      'https://www.googleapis.com/auth/spreadsheets',
      'https://www.googleapis.com/auth/drive.readonly'
    ]
  },

  openrouter: {
    apiKey: process.env.OPENROUTER_API_KEY,
    model: process.env.OPENROUTER_MODEL || 'moonshot/kimi-k2',
    baseUrl: 'https://openrouter.ai/api/v1'
  },

  sheets: {
    spreadsheetId: process.env.SPREADSHEET_ID
  },

  email: {
    checkInterval: parseInt(process.env.EMAIL_CHECK_INTERVAL) || 60000, // 1 minute
    maxEmailsPerBatch: parseInt(process.env.MAX_EMAILS_PER_BATCH) || 10
  },

  webhook: {
    secret: process.env.WEBHOOK_SECRET,
    url: process.env.WEBHOOK_URL
  },

  logging: {
    level: process.env.LOG_LEVEL || 'info'
  }
};

// Validation
const requiredEnvVars = [
  'GOOGLE_CLIENT_ID',
  'GOOGLE_CLIENT_SECRET',
  'GOOGLE_API_KEY',
  'OPENROUTER_API_KEY'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName] || process.env[varName].includes('PLACEHOLDER'));
if (missingVars.length > 0) {
  console.warn('Missing environment variables:', missingVars);
  console.warn('Some features may not work until all credentials are provided.');
  console.warn('Server will start in demo mode.');
}

module.exports = config;
