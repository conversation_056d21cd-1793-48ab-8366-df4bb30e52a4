console.log('Testing basic Node.js functionality...');

try {
  const config = require('./config/config');
  console.log('✅ Config loaded successfully');
  console.log('Port:', config.server.port);
} catch (error) {
  console.error('❌ Error loading config:', error.message);
  console.error(error.stack);
}

try {
  const express = require('express');
  console.log('✅ Express loaded successfully');
} catch (error) {
  console.error('❌ Error loading Express:', error.message);
}

console.log('Test completed.');
