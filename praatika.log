[32minfo[39m: Email monitoring scheduled (every minute) {"service":"praatika-agent","timestamp":"2025-08-01T19:39:58.911Z"}
[32minfo[39m: Praatika Agent initialized successfully {"service":"praatika-agent","timestamp":"2025-08-01T19:39:58.914Z"}
[32minfo[39m: Praatika Agent running on port 3000 {"service":"praatika-agent","timestamp":"2025-08-01T19:39:58.914Z"}
[32minfo[39m: Environment: development {"service":"praatika-agent","timestamp":"2025-08-01T19:39:58.914Z"}
[32minfo[39m: Available endpoints: {"service":"praatika-agent","timestamp":"2025-08-01T19:39:58.914Z"}
[32minfo[39m:   GET  / - Status and info {"service":"praatika-agent","timestamp":"2025-08-01T19:39:58.915Z"}
[32minfo[39m:   GET  /auth/google - Start OAuth flow {"service":"praatika-agent","timestamp":"2025-08-01T19:39:58.915Z"}
[32minfo[39m:   GET  /auth/status - Check authentication {"service":"praatika-agent","timestamp":"2025-08-01T19:39:58.915Z"}
[32minfo[39m:   GET  /api/health - Health check {"service":"praatika-agent","timestamp":"2025-08-01T19:39:58.915Z"}
[32minfo[39m:   GET  /api/emails/check - Manual email check {"service":"praatika-agent","timestamp":"2025-08-01T19:39:58.915Z"}
[32minfo[39m:   GET  /api/stats - Processing statistics {"service":"praatika-agent","timestamp":"2025-08-01T19:39:58.915Z"}
