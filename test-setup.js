// Quick test to verify the setup
const config = require('./config/config');

console.log('🤖 Praatika Agent Setup Test\n');

// Test configuration loading
console.log('✅ Configuration loaded successfully');
console.log(`   Server Port: ${config.server.port}`);
console.log(`   Environment: ${config.server.env}`);

// Check required environment variables
const requiredVars = [
  'GOOGLE_CLIENT_ID',
  'GOOGLE_CLIENT_SECRET',
  'GOOGLE_API_KEY',
  'OPENROUTER_API_KEY'
];

const optionalVars = [
  'SPREADSHEET_ID'
];

console.log('\n📋 Environment Variables Check:');
let missingVars = [];

requiredVars.forEach(varName => {
  const value = process.env[varName];
  if (!value || value.includes('PLACEHOLDER')) {
    console.log(`   ❌ ${varName}: Missing or placeholder`);
    missingVars.push(varName);
  } else {
    console.log(`   ✅ ${varName}: Configured`);
  }
});

console.log('\n📋 Optional Variables:');
optionalVars.forEach(varName => {
  const value = process.env[varName];
  if (!value || value.includes('PLACEHOLDER')) {
    console.log(`   ⚠️  ${varName}: Not set (can be configured via UI)`);
  } else {
    console.log(`   ✅ ${varName}: Configured`);
  }
});

console.log('\n🔧 Next Steps:');
if (missingVars.length > 0) {
  console.log('   1. Update .env file with your actual credentials:');
  missingVars.forEach(varName => {
    console.log(`      - ${varName}`);
  });
  console.log('   2. Run: npm start');
  console.log('   3. Visit: http://localhost:3000 (Professional UI Dashboard)');
  console.log('   4. Complete OAuth authentication via the UI');
} else {
  console.log('   1. Run: npm start');
  console.log('   2. Visit: http://localhost:3000 (Professional UI Dashboard)');
  console.log('   3. Complete OAuth authentication via the UI');
  console.log('   4. Select your Google Sheet via the UI');
  console.log('   5. Agent will start processing emails automatically!');
}

console.log('\n📚 Documentation: See README.md for detailed setup instructions');
console.log('🌐 Status endpoint: http://localhost:3000');
console.log('🔐 Auth endpoint: http://localhost:3000/auth/google');
console.log('📊 API docs: http://localhost:3000/api/health');
