<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Praatika - AI Email Agent Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <div class="app">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-robot"></i>
                    <h1>Praatika</h1>
                    <span class="subtitle">AI Email Agent</span>
                </div>
                <div class="header-actions">
                    <div class="status-indicator" id="statusIndicator">
                        <i class="fas fa-circle"></i>
                        <span id="statusText">Checking...</span>
                    </div>
                    <button class="btn btn-outline" id="refreshBtn">
                        <i class="fas fa-sync-alt"></i>
                        Refresh
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main">
            <!-- Authentication Section -->
            <section class="card" id="authSection">
                <div class="card-header">
                    <h2><i class="fas fa-key"></i> Authentication</h2>
                </div>
                <div class="card-content">
                    <div id="authStatus" class="auth-status">
                        <div class="loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            Checking authentication status...
                        </div>
                    </div>
                </div>
            </section>

            <!-- Configuration Section -->
            <section class="card" id="configSection" style="display: none;">
                <div class="card-header">
                    <h2><i class="fas fa-cog"></i> Configuration</h2>
                </div>
                <div class="card-content">
                    <div class="config-grid">
                        <div class="config-item">
                            <label for="spreadsheetSelect">Select Google Sheet:</label>
                            <select id="spreadsheetSelect" class="form-select">
                                <option value="">Loading spreadsheets...</option>
                            </select>
                        </div>
                        <div class="config-item">
                            <label for="sheetSelect">Select Sheet Tab:</label>
                            <select id="sheetSelect" class="form-select">
                                <option value="">Select a spreadsheet first</option>
                            </select>
                        </div>
                        <div class="config-item">
                            <label for="columnsInput">Column Headers (comma-separated):</label>
                            <input type="text" id="columnsInput" class="form-input" 
                                   placeholder="Subject, From, Date, Classification, Summary">
                        </div>
                        <div class="config-actions">
                            <button class="btn btn-primary" id="saveConfigBtn">
                                <i class="fas fa-save"></i>
                                Save Configuration
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Dashboard Grid -->
            <div class="dashboard-grid" id="dashboardGrid" style="display: none;">
                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalEmails">0</h3>
                            <p>Total Processed</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon relevant">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="relevantEmails">0</h3>
                            <p>Relevant Emails</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon spam">
                            <i class="fas fa-ban"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="spamEmails">0</h3>
                            <p>Spam Filtered</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon personal">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="personalEmails">0</h3>
                            <p>Personal Emails</p>
                        </div>
                    </div>
                </div>

                <!-- Actions Panel -->
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-play"></i> Actions</h2>
                    </div>
                    <div class="card-content">
                        <div class="actions-grid">
                            <button class="btn btn-primary" id="checkEmailsBtn">
                                <i class="fas fa-envelope-open"></i>
                                Check Emails Now
                            </button>
                            <button class="btn btn-secondary" id="viewSheetsBtn">
                                <i class="fas fa-table"></i>
                                View Sheet Data
                            </button>
                            <button class="btn btn-warning" id="clearDataBtn">
                                <i class="fas fa-trash"></i>
                                Clear Sheet Data
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Activity Log -->
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-list"></i> Recent Activity</h2>
                        <button class="btn btn-sm btn-outline" id="clearLogBtn">
                            <i class="fas fa-eraser"></i>
                            Clear Log
                        </button>
                    </div>
                    <div class="card-content">
                        <div class="activity-log" id="activityLog">
                            <div class="log-entry">
                                <span class="timestamp">System started</span>
                                <span class="message">Praatika agent initialized</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2024 Praatika AI Email Agent. Built with ❤️ for automation.</p>
        </footer>
    </div>

    <!-- Modals -->
    <div class="modal" id="dataModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Sheet Data</h3>
                <button class="modal-close" id="closeDataModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="table-container">
                    <table id="dataTable" class="data-table">
                        <thead id="dataTableHead"></thead>
                        <tbody id="dataTableBody"></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
