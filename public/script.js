class PraatikaUI {
    constructor() {
        this.isAuthenticated = false;
        this.currentConfig = {};
        this.activityLog = [];
        this.init();
    }

    init() {
        this.bindEvents();
        this.checkAuthStatus();
        this.loadActivityLog();
        
        // Auto-refresh every 30 seconds
        setInterval(() => {
            if (this.isAuthenticated) {
                this.updateStats();
            }
        }, 30000);
    }

    bindEvents() {
        // Refresh button
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.refreshAll();
        });

        // Authentication
        document.getElementById('authSection').addEventListener('click', (e) => {
            if (e.target.id === 'authenticateBtn') {
                window.location.href = '/auth/google';
            }
        });

        // Configuration
        document.getElementById('spreadsheetSelect').addEventListener('change', (e) => {
            this.loadSheets(e.target.value);
        });

        document.getElementById('saveConfigBtn').addEventListener('click', () => {
            this.saveConfiguration();
        });

        // Actions
        document.getElementById('checkEmailsBtn').addEventListener('click', () => {
            this.checkEmails();
        });

        document.getElementById('viewSheetsBtn').addEventListener('click', () => {
            this.viewSheetData();
        });

        document.getElementById('clearDataBtn').addEventListener('click', () => {
            this.clearSheetData();
        });

        document.getElementById('clearLogBtn').addEventListener('click', () => {
            this.clearActivityLog();
        });

        // Modal
        document.getElementById('closeDataModal').addEventListener('click', () => {
            this.closeModal();
        });

        // Close modal on outside click
        document.getElementById('dataModal').addEventListener('click', (e) => {
            if (e.target.id === 'dataModal') {
                this.closeModal();
            }
        });
    }

    async checkAuthStatus() {
        try {
            const response = await fetch('/auth/status');
            const data = await response.json();
            
            this.isAuthenticated = data.authenticated;
            this.updateAuthUI(data);
            
            if (this.isAuthenticated) {
                this.loadConfiguration();
                this.loadSpreadsheets();
                this.updateStats();
                this.showDashboard();
            }
        } catch (error) {
            console.error('Error checking auth status:', error);
            this.updateAuthUI({ authenticated: false, message: 'Error checking authentication' });
        }
    }

    updateAuthUI(data) {
        const authStatus = document.getElementById('authStatus');
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        
        if (data.authenticated) {
            authStatus.innerHTML = `
                <div class="auth-success">
                    <i class="fas fa-check-circle"></i>
                    <h3>Authenticated Successfully!</h3>
                    <p>${data.message}</p>
                </div>
            `;
            statusIndicator.className = 'status-indicator online';
            statusText.textContent = 'Online';
        } else {
            authStatus.innerHTML = `
                <div class="auth-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <h3>Authentication Required</h3>
                    <p>${data.message}</p>
                    <button class="btn btn-primary" id="authenticateBtn">
                        <i class="fas fa-sign-in-alt"></i>
                        Authenticate with Google
                    </button>
                </div>
            `;
            statusIndicator.className = 'status-indicator offline';
            statusText.textContent = 'Offline';
        }
    }

    async loadSpreadsheets() {
        try {
            const response = await fetch('/api/spreadsheets');
            const data = await response.json();
            
            const select = document.getElementById('spreadsheetSelect');
            select.innerHTML = '<option value="">Select a spreadsheet...</option>';
            
            data.spreadsheets.forEach(sheet => {
                const option = document.createElement('option');
                option.value = sheet.id;
                option.textContent = sheet.name;
                select.appendChild(option);
            });
            
            this.addToLog('Loaded available spreadsheets');
        } catch (error) {
            console.error('Error loading spreadsheets:', error);
            this.addToLog('Error loading spreadsheets', 'error');
        }
    }

    async loadSheets(spreadsheetId) {
        if (!spreadsheetId) return;
        
        try {
            const response = await fetch(`/api/spreadsheets/${spreadsheetId}/sheets`);
            const data = await response.json();
            
            const select = document.getElementById('sheetSelect');
            select.innerHTML = '<option value="">Select a sheet...</option>';
            
            data.sheets.forEach(sheet => {
                const option = document.createElement('option');
                option.value = sheet.title;
                option.textContent = sheet.title;
                select.appendChild(option);
            });
            
            this.addToLog(`Loaded sheets for spreadsheet`);
        } catch (error) {
            console.error('Error loading sheets:', error);
            this.addToLog('Error loading sheets', 'error');
        }
    }

    async loadConfiguration() {
        try {
            const response = await fetch('/api/config/sheet');
            const data = await response.json();
            
            this.currentConfig = data.config;
            
            // Load columns
            const columnsResponse = await fetch('/api/columns');
            const columnsData = await columnsResponse.json();
            
            document.getElementById('columnsInput').value = columnsData.columns.join(', ');
        } catch (error) {
            console.error('Error loading configuration:', error);
        }
    }

    async saveConfiguration() {
        const spreadsheetId = document.getElementById('spreadsheetSelect').value;
        const sheetName = document.getElementById('sheetSelect').value;
        const columns = document.getElementById('columnsInput').value
            .split(',')
            .map(col => col.trim())
            .filter(col => col);
        
        if (!spreadsheetId) {
            alert('Please select a spreadsheet');
            return;
        }
        
        try {
            // Save sheet configuration
            const sheetResponse = await fetch('/api/config/sheet', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ spreadsheetId, sheetName })
            });
            
            // Save columns
            const columnsResponse = await fetch('/api/columns/update', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ columns })
            });
            
            if (sheetResponse.ok && columnsResponse.ok) {
                this.addToLog('Configuration saved successfully');
                alert('Configuration saved successfully!');
            } else {
                throw new Error('Failed to save configuration');
            }
        } catch (error) {
            console.error('Error saving configuration:', error);
            this.addToLog('Error saving configuration', 'error');
            alert('Error saving configuration');
        }
    }

    async updateStats() {
        try {
            const response = await fetch('/api/stats');
            const data = await response.json();
            
            if (data.success) {
                document.getElementById('totalEmails').textContent = data.stats.totalProcessed || 0;
                document.getElementById('relevantEmails').textContent = data.stats.relevant || 0;
                document.getElementById('spamEmails').textContent = data.stats.spam || 0;
                document.getElementById('personalEmails').textContent = data.stats.personal || 0;
            }
        } catch (error) {
            console.error('Error updating stats:', error);
        }
    }

    async checkEmails() {
        const btn = document.getElementById('checkEmailsBtn');
        const originalText = btn.innerHTML;
        
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';
        btn.disabled = true;
        
        try {
            const response = await fetch('/api/emails/check');
            const data = await response.json();
            
            if (data.success) {
                this.addToLog(`Email check completed: ${data.results.processed} emails processed`);
                this.updateStats();
            } else {
                throw new Error(data.error);
            }
        } catch (error) {
            console.error('Error checking emails:', error);
            this.addToLog('Error checking emails', 'error');
        } finally {
            btn.innerHTML = originalText;
            btn.disabled = false;
        }
    }

    async viewSheetData() {
        try {
            const response = await fetch('/api/sheets/data');
            const data = await response.json();
            
            if (data.success && data.data.length > 0) {
                this.showDataModal(data.data);
            } else {
                alert('No data found in the sheet');
            }
        } catch (error) {
            console.error('Error viewing sheet data:', error);
            alert('Error loading sheet data');
        }
    }

    async clearSheetData() {
        if (!confirm('Are you sure you want to clear all sheet data? This cannot be undone.')) {
            return;
        }
        
        try {
            const response = await fetch('/api/sheets/clear', { method: 'POST' });
            const data = await response.json();
            
            if (data.success) {
                this.addToLog('Sheet data cleared');
                this.updateStats();
                alert('Sheet data cleared successfully');
            } else {
                throw new Error(data.error);
            }
        } catch (error) {
            console.error('Error clearing sheet data:', error);
            this.addToLog('Error clearing sheet data', 'error');
            alert('Error clearing sheet data');
        }
    }

    showDataModal(data) {
        const modal = document.getElementById('dataModal');
        const thead = document.getElementById('dataTableHead');
        const tbody = document.getElementById('dataTableBody');
        
        // Clear existing content
        thead.innerHTML = '';
        tbody.innerHTML = '';
        
        if (data.length > 0) {
            // Create header
            const headerRow = document.createElement('tr');
            data[0].forEach(header => {
                const th = document.createElement('th');
                th.textContent = header;
                headerRow.appendChild(th);
            });
            thead.appendChild(headerRow);
            
            // Create rows
            data.slice(1).forEach(row => {
                const tr = document.createElement('tr');
                row.forEach(cell => {
                    const td = document.createElement('td');
                    td.textContent = cell || '';
                    tr.appendChild(td);
                });
                tbody.appendChild(tr);
            });
        }
        
        modal.style.display = 'block';
    }

    closeModal() {
        document.getElementById('dataModal').style.display = 'none';
    }

    showDashboard() {
        document.getElementById('configSection').style.display = 'block';
        document.getElementById('dashboardGrid').style.display = 'block';
    }

    addToLog(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = { timestamp, message, type };
        
        this.activityLog.unshift(logEntry);
        if (this.activityLog.length > 50) {
            this.activityLog = this.activityLog.slice(0, 50);
        }
        
        this.updateLogUI();
        this.saveActivityLog();
    }

    updateLogUI() {
        const logContainer = document.getElementById('activityLog');
        logContainer.innerHTML = '';
        
        this.activityLog.forEach(entry => {
            const div = document.createElement('div');
            div.className = `log-entry ${entry.type}`;
            div.innerHTML = `
                <span class="timestamp">${entry.timestamp}</span>
                <span class="message">${entry.message}</span>
            `;
            logContainer.appendChild(div);
        });
    }

    clearActivityLog() {
        this.activityLog = [];
        this.updateLogUI();
        this.saveActivityLog();
    }

    saveActivityLog() {
        localStorage.setItem('praatika_activity_log', JSON.stringify(this.activityLog));
    }

    loadActivityLog() {
        const saved = localStorage.getItem('praatika_activity_log');
        if (saved) {
            this.activityLog = JSON.parse(saved);
            this.updateLogUI();
        }
    }

    async refreshAll() {
        const btn = document.getElementById('refreshBtn');
        const icon = btn.querySelector('i');
        
        icon.classList.add('fa-spin');
        
        try {
            await this.checkAuthStatus();
            if (this.isAuthenticated) {
                await this.updateStats();
                await this.loadSpreadsheets();
            }
            this.addToLog('Dashboard refreshed');
        } catch (error) {
            console.error('Error refreshing:', error);
            this.addToLog('Error refreshing dashboard', 'error');
        } finally {
            icon.classList.remove('fa-spin');
        }
    }
}

// Initialize the UI when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new PraatikaUI();
});
