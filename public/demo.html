<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Praatika - AI Email Agent Dashboard (Demo Mode)</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <div class="app">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-robot"></i>
                    <h1>Praatika</h1>
                    <span class="subtitle">AI Email Agent (Demo Mode)</span>
                </div>
                <div class="header-actions">
                    <div class="status-indicator online">
                        <i class="fas fa-circle"></i>
                        <span>Demo Mode</span>
                    </div>
                    <button class="btn btn-outline" onclick="location.reload()">
                        <i class="fas fa-sync-alt"></i>
                        Refresh
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main">
            <!-- Demo Notice -->
            <section class="card">
                <div class="card-header">
                    <h2><i class="fas fa-info-circle"></i> Demo Mode</h2>
                </div>
                <div class="card-content">
                    <div class="demo-notice">
                        <p><strong>This is a demonstration of the Praatika AI Email Agent interface.</strong></p>
                        <p>To use the full functionality, you need to:</p>
                        <ul>
                            <li>Add your Google Client Secret to the .env file</li>
                            <li>Complete OAuth authentication</li>
                            <li>Select your Google Sheets</li>
                        </ul>
                        <p>The interface below shows how the agent will look when fully configured.</p>
                    </div>
                </div>
            </section>

            <!-- Configuration Section -->
            <section class="card">
                <div class="card-header">
                    <h2><i class="fas fa-cog"></i> Configuration</h2>
                </div>
                <div class="card-content">
                    <div class="config-grid">
                        <div class="config-item">
                            <label for="demoSpreadsheetSelect">Select Google Sheet:</label>
                            <select id="demoSpreadsheetSelect" class="form-select">
                                <option value="demo1">📊 Sales Leads Tracker</option>
                                <option value="demo2">📧 Customer Support Emails</option>
                                <option value="demo3">💼 Business Inquiries</option>
                            </select>
                        </div>
                        <div class="config-item">
                            <label for="demoSheetSelect">Select Sheet Tab:</label>
                            <select id="demoSheetSelect" class="form-select">
                                <option value="EmailData">EmailData</option>
                                <option value="Processed">Processed</option>
                                <option value="Archive">Archive</option>
                            </select>
                        </div>
                        <div class="config-item">
                            <label for="demoColumnsInput">Column Headers (comma-separated):</label>
                            <input type="text" id="demoColumnsInput" class="form-input" 
                                   value="Subject, From, Date, Classification, Summary, Priority, Status">
                        </div>
                        <div class="config-actions">
                            <button class="btn btn-primary" onclick="showDemoAlert('Configuration saved!')">
                                <i class="fas fa-save"></i>
                                Save Configuration
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Dashboard Grid -->
            <div class="dashboard-grid">
                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="stat-content">
                            <h3>247</h3>
                            <p>Total Processed</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon relevant">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3>89</h3>
                            <p>Relevant Emails</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon spam">
                            <i class="fas fa-ban"></i>
                        </div>
                        <div class="stat-content">
                            <h3>142</h3>
                            <p>Spam Filtered</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon personal">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="stat-content">
                            <h3>16</h3>
                            <p>Personal Emails</p>
                        </div>
                    </div>
                </div>

                <!-- Actions Panel -->
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-play"></i> Actions</h2>
                    </div>
                    <div class="card-content">
                        <div class="actions-grid">
                            <button class="btn btn-primary" onclick="simulateEmailCheck()">
                                <i class="fas fa-envelope-open"></i>
                                Check Emails Now
                            </button>
                            <button class="btn btn-secondary" onclick="showDemoData()">
                                <i class="fas fa-table"></i>
                                View Sheet Data
                            </button>
                            <button class="btn btn-warning" onclick="showDemoAlert('Sheet data cleared!')">
                                <i class="fas fa-trash"></i>
                                Clear Sheet Data
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Activity Log -->
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-list"></i> Recent Activity</h2>
                        <button class="btn btn-sm btn-outline" onclick="clearDemoLog()">
                            <i class="fas fa-eraser"></i>
                            Clear Log
                        </button>
                    </div>
                    <div class="card-content">
                        <div class="activity-log" id="demoActivityLog">
                            <div class="log-entry">
                                <span class="timestamp">14:32:15</span>
                                <span class="message">Email check completed: 3 emails processed</span>
                            </div>
                            <div class="log-entry">
                                <span class="timestamp">14:31:42</span>
                                <span class="message">Added relevant email to sheets: New Business Inquiry</span>
                            </div>
                            <div class="log-entry">
                                <span class="timestamp">14:30:18</span>
                                <span class="message">Classified email as SPAM: Promotional offer</span>
                            </div>
                            <div class="log-entry">
                                <span class="timestamp">14:29:55</span>
                                <span class="message">Configuration saved successfully</span>
                            </div>
                            <div class="log-entry">
                                <span class="timestamp">14:28:30</span>
                                <span class="message">Praatika agent initialized</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2024 Praatika AI Email Agent. Built with ❤️ for automation.</p>
        </footer>
    </div>

    <!-- Demo Data Modal -->
    <div class="modal" id="demoDataModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Sheet Data (Demo)</h3>
                <button class="modal-close" onclick="closeDemoModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Subject</th>
                                <th>From</th>
                                <th>Date</th>
                                <th>Classification</th>
                                <th>Summary</th>
                                <th>Priority</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>New Business Inquiry</td>
                                <td><EMAIL></td>
                                <td>2024-08-01 14:30</td>
                                <td>RELEVANT</td>
                                <td>Potential client interested in our services</td>
                                <td>High</td>
                            </tr>
                            <tr>
                                <td>Partnership Proposal</td>
                                <td><EMAIL></td>
                                <td>2024-08-01 13:45</td>
                                <td>RELEVANT</td>
                                <td>Strategic partnership opportunity</td>
                                <td>Medium</td>
                            </tr>
                            <tr>
                                <td>Customer Support Request</td>
                                <td><EMAIL></td>
                                <td>2024-08-01 12:20</td>
                                <td>RELEVANT</td>
                                <td>Technical issue requiring assistance</td>
                                <td>High</td>
                            </tr>
                            <tr>
                                <td>Invoice Payment Confirmation</td>
                                <td><EMAIL></td>
                                <td>2024-08-01 11:15</td>
                                <td>RELEVANT</td>
                                <td>Payment processed successfully</td>
                                <td>Low</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showDemoAlert(message) {
            alert('Demo Mode: ' + message);
        }

        function simulateEmailCheck() {
            const btn = event.target;
            const originalText = btn.innerHTML;
            
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';
            btn.disabled = true;
            
            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.disabled = false;
                addDemoLogEntry('Email check completed: 2 new emails processed');
                showDemoAlert('Email check completed: 2 new emails processed');
            }, 2000);
        }

        function showDemoData() {
            document.getElementById('demoDataModal').style.display = 'block';
        }

        function closeDemoModal() {
            document.getElementById('demoDataModal').style.display = 'none';
        }

        function addDemoLogEntry(message) {
            const log = document.getElementById('demoActivityLog');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.innerHTML = `
                <span class="timestamp">${timestamp}</span>
                <span class="message">${message}</span>
            `;
            log.insertBefore(entry, log.firstChild);
        }

        function clearDemoLog() {
            document.getElementById('demoActivityLog').innerHTML = `
                <div class="log-entry">
                    <span class="timestamp">${new Date().toLocaleTimeString()}</span>
                    <span class="message">Activity log cleared</span>
                </div>
            `;
        }

        // Close modal on outside click
        document.getElementById('demoDataModal').addEventListener('click', (e) => {
            if (e.target.id === 'demoDataModal') {
                closeDemoModal();
            }
        });
    </script>
</body>
</html>
