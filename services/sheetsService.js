const config = require('../config/config');
const logger = require('../utils/logger');

class SheetsService {
  constructor(oauthHandler) {
    this.oauthHandler = oauthHandler;
    this.spreadsheetId = config.sheets.spreadsheetId;
    this.sheetName = 'EmailData'; // Default sheet name
  }

  /**
   * Set the spreadsheet ID to work with
   */
  setSpreadsheetId(spreadsheetId) {
    this.spreadsheetId = spreadsheetId;
  }

  /**
   * Set the sheet name to work with
   */
  setSheetName(sheetName) {
    this.sheetName = sheetName;
  }

  /**
   * Get list of all spreadsheets accessible to the user
   */
  async getAvailableSpreadsheets() {
    try {
      await this.oauthHandler.refreshTokenIfNeeded();
      const drive = this.oauthHandler.getDriveClient();

      const response = await drive.files.list({
        q: "mimeType='application/vnd.google-apps.spreadsheet'",
        fields: 'files(id, name, modifiedTime)',
        orderBy: 'modifiedTime desc',
        pageSize: 50
      });

      return response.data.files || [];
    } catch (error) {
      logger.error('Error getting available spreadsheets:', error);
      return [];
    }
  }

  /**
   * Get list of sheets in a spreadsheet
   */
  async getAvailableSheets(spreadsheetId = null) {
    try {
      await this.oauthHandler.refreshTokenIfNeeded();
      const sheets = this.oauthHandler.getSheetsClient();
      const targetSpreadsheetId = spreadsheetId || this.spreadsheetId;

      const response = await sheets.spreadsheets.get({
        spreadsheetId: targetSpreadsheetId
      });

      return response.data.sheets.map(sheet => ({
        id: sheet.properties.sheetId,
        title: sheet.properties.title,
        index: sheet.properties.index
      }));
    } catch (error) {
      logger.error('Error getting available sheets:', error);
      return [];
    }
  }

  /**
   * Initialize the Google Sheet with headers
   */
  async initializeSheet(columnHeaders) {
    try {
      await this.oauthHandler.refreshTokenIfNeeded();
      const sheets = this.oauthHandler.getSheetsClient();

      // Check if sheet exists, create if not
      await this.ensureSheetExists();

      // Check if headers already exist
      const existingHeaders = await this.getHeaders();
      if (existingHeaders.length === 0) {
        // Add headers to the sheet
        await sheets.spreadsheets.values.update({
          spreadsheetId: this.spreadsheetId,
          range: `${this.sheetName}!A1:${this.getColumnLetter(columnHeaders.length)}1`,
          valueInputOption: 'RAW',
          requestBody: {
            values: [columnHeaders]
          }
        });

        logger.info('Sheet initialized with headers:', columnHeaders);
      } else {
        logger.info('Sheet already has headers');
      }

      return true;
    } catch (error) {
      logger.error('Error initializing sheet:', error);
      throw error;
    }
  }

  /**
   * Add email data to the sheet
   */
  async addEmailData(emailData, columnHeaders) {
    try {
      await this.oauthHandler.refreshTokenIfNeeded();
      const sheets = this.oauthHandler.getSheetsClient();

      // Prepare row data based on column headers
      const rowData = columnHeaders.map(header => {
        const value = emailData[header];
        if (value === null || value === undefined) {
          return '';
        }
        if (value instanceof Date) {
          return value.toISOString();
        }
        return String(value);
      });

      // Add timestamp if not already included
      if (!columnHeaders.includes('ProcessedAt')) {
        rowData.push(new Date().toISOString());
      }

      // Find the next empty row
      const nextRow = await this.getNextEmptyRow();

      // Append the data
      await sheets.spreadsheets.values.append({
        spreadsheetId: this.spreadsheetId,
        range: `${this.sheetName}!A${nextRow}`,
        valueInputOption: 'RAW',
        insertDataOption: 'INSERT_ROWS',
        requestBody: {
          values: [rowData]
        }
      });

      logger.info(`Added email data to row ${nextRow}`);
      return nextRow;
    } catch (error) {
      logger.error('Error adding email data to sheet:', error);
      throw error;
    }
  }

  /**
   * Get existing headers from the sheet
   */
  async getHeaders() {
    try {
      await this.oauthHandler.refreshTokenIfNeeded();
      const sheets = this.oauthHandler.getSheetsClient();

      const response = await sheets.spreadsheets.values.get({
        spreadsheetId: this.spreadsheetId,
        range: `${this.sheetName}!1:1`
      });

      return response.data.values ? response.data.values[0] : [];
    } catch (error) {
      logger.error('Error getting headers:', error);
      return [];
    }
  }

  /**
   * Get the next empty row number
   */
  async getNextEmptyRow() {
    try {
      await this.oauthHandler.refreshTokenIfNeeded();
      const sheets = this.oauthHandler.getSheetsClient();

      const response = await sheets.spreadsheets.values.get({
        spreadsheetId: this.spreadsheetId,
        range: `${this.sheetName}!A:A`
      });

      const values = response.data.values || [];
      return values.length + 1;
    } catch (error) {
      logger.error('Error getting next empty row:', error);
      return 2; // Default to row 2 if error
    }
  }

  /**
   * Ensure the sheet exists, create if not
   */
  async ensureSheetExists() {
    try {
      await this.oauthHandler.refreshTokenIfNeeded();
      const sheets = this.oauthHandler.getSheetsClient();

      // Get spreadsheet metadata
      const spreadsheet = await sheets.spreadsheets.get({
        spreadsheetId: this.spreadsheetId
      });

      // Check if our sheet exists
      const sheetExists = spreadsheet.data.sheets.some(
        sheet => sheet.properties.title === this.sheetName
      );

      if (!sheetExists) {
        // Create the sheet
        await sheets.spreadsheets.batchUpdate({
          spreadsheetId: this.spreadsheetId,
          requestBody: {
            requests: [{
              addSheet: {
                properties: {
                  title: this.sheetName
                }
              }
            }]
          }
        });

        logger.info(`Created new sheet: ${this.sheetName}`);
      }
    } catch (error) {
      logger.error('Error ensuring sheet exists:', error);
      throw error;
    }
  }

  /**
   * Convert column number to letter (A, B, C, etc.)
   */
  getColumnLetter(columnNumber) {
    let letter = '';
    while (columnNumber > 0) {
      columnNumber--;
      letter = String.fromCharCode(65 + (columnNumber % 26)) + letter;
      columnNumber = Math.floor(columnNumber / 26);
    }
    return letter;
  }

  /**
   * Get all data from the sheet
   */
  async getAllData() {
    try {
      await this.oauthHandler.refreshTokenIfNeeded();
      const sheets = this.oauthHandler.getSheetsClient();

      const response = await sheets.spreadsheets.values.get({
        spreadsheetId: this.spreadsheetId,
        range: `${this.sheetName}!A:Z`
      });

      return response.data.values || [];
    } catch (error) {
      logger.error('Error getting all data:', error);
      return [];
    }
  }

  /**
   * Clear all data from the sheet (except headers)
   */
  async clearData() {
    try {
      await this.oauthHandler.refreshTokenIfNeeded();
      const sheets = this.oauthHandler.getSheetsClient();

      const nextRow = await this.getNextEmptyRow();
      if (nextRow > 2) {
        await sheets.spreadsheets.values.clear({
          spreadsheetId: this.spreadsheetId,
          range: `${this.sheetName}!A2:Z${nextRow - 1}`
        });

        logger.info('Cleared sheet data');
      }
    } catch (error) {
      logger.error('Error clearing sheet data:', error);
      throw error;
    }
  }
}

module.exports = SheetsService;
