# Praatika Email Agent

An AI-powered email automation agent with a professional web dashboard that fetches emails from Gmail, processes them using Kimi K2 LLM via OpenRouter, and automatically populates Google Sheets with relevant data.

## 🎯 **Professional Web Dashboard**

Praatika comes with a beautiful, responsive web interface that provides:
- **Real-time monitoring** of email processing
- **Interactive sheet selection** from your Google Drive
- **Live statistics** and activity logs
- **One-click email processing** and configuration
- **Professional presentation** suitable for office demonstrations

## Features

- **Automated Email Monitoring**: Continuously checks Gmail every minute for new emails
- **AI-Powered Classification**: Uses Kimi K2 LLM to classify emails as RELEVANT, SPAM, or PERSONAL
- **Smart Data Extraction**: Extracts structured data from relevant emails
- **Google Sheets Integration**: Automatically populates predefined columns in Google Sheets
- **OAuth Security**: Secure authentication with Google services
- **Real-time Processing**: Processes emails one by one as they arrive
- **Webhook Support**: Optional real-time notifications via Gmail push notifications
- **RESTful API**: Complete API for monitoring and management

## Prerequisites

1. **Google Cloud Console Setup**:
   - Gmail API enabled
   - Google Sheets API enabled
   - OAuth 2.0 credentials configured
   - Client ID: `************-j5dvm8f8tkmi7gd1vimfhndnt75ofq03.apps.googleusercontent.com`
   - API Key: `AIzaSyCHK5v6aCrIXQAakOtAbJjPA1MEpDWEEMo`

2. **OpenRouter Account**:
   - API key for Kimi K2 LLM access

3. **Google Sheets**:
   - Create a Google Sheet and note the Spreadsheet ID

## Installation

1. **Clone and Install Dependencies**:
```bash
npm install
```

2. **Environment Configuration**:
```bash
cp .env.example .env
```

Edit `.env` file with your credentials:
```env
# Server Configuration
PORT=3000
NODE_ENV=development

# Google OAuth Credentials
GOOGLE_CLIENT_ID=************-j5dvm8f8tkmi7gd1vimfhndnt75ofq03.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
GOOGLE_API_KEY=AIzaSyCHK5v6aCrIXQAakOtAbJjPA1MEpDWEEMo
GOOGLE_REDIRECT_URI=http://localhost:3000/auth/google/callback

# OpenRouter Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_MODEL=moonshot/kimi-k2

# Google Sheets Configuration
SPREADSHEET_ID=your_google_sheets_id_here
```

3. **Start the Agent**:
```bash
npm start
```

4. **Access the Dashboard**:
   - Open your browser and go to: `http://localhost:3000`
   - If credentials are missing, you'll see a demo mode
   - Once configured, you'll access the full dashboard

For development with auto-restart:
```bash
npm run dev
```

## Setup Process

### 1. Quick Start (Demo Mode)
1. Start the server: `npm start`
2. Visit: `http://localhost:3000`
3. You'll see the demo dashboard showing the interface
4. Perfect for office presentations and demonstrations

### 2. Full Setup (Production Mode)
1. Add your Google Client Secret to `.env`
2. Visit: `http://localhost:3000`
3. Click "Authenticate with Google" in the dashboard
4. Complete OAuth flow through the UI
5. Use the dashboard to select your Google Sheets
6. Configure column headers through the interface
7. Start processing emails with one click!

### 3. Dashboard Features
- **Authentication Status**: Real-time connection status
- **Sheet Selection**: Browse and select from your Google Sheets
- **Configuration**: Set column headers and processing options
- **Statistics**: Live email processing metrics
- **Activity Log**: Real-time processing updates
- **Manual Controls**: Trigger email checks and view data

## API Endpoints

### Authentication
- `GET /auth/google` - Start OAuth flow
- `GET /auth/google/callback` - OAuth callback
- `GET /auth/status` - Check authentication status
- `POST /auth/logout` - Clear authentication

### Email Processing
- `GET /api/emails/check` - Manually trigger email check
- `POST /api/emails/process-custom` - Process with custom columns
- `GET /api/stats` - Get processing statistics

### Google Sheets
- `GET /api/sheets/data` - Get all sheet data
- `POST /api/sheets/clear` - Clear sheet data
- `GET /api/columns` - Get current column headers
- `POST /api/columns/update` - Update column headers

### System
- `GET /` - Agent status and info
- `GET /api/health` - Health check

### Webhooks (Optional)
- `POST /webhook/gmail` - Gmail push notifications
- `GET /webhook/gmail/verify` - Verify webhook endpoint

## Default Column Structure

The agent creates these default columns in Google Sheets:
- **Subject**: Email subject line
- **From**: Sender email address
- **Date**: Email date/time
- **Classification**: AI classification (RELEVANT/SPAM/PERSONAL)
- **Summary**: AI-generated summary
- **EmailId**: Unique Gmail message ID
- **ProcessedAt**: Processing timestamp

## Custom Column Configuration

You can define custom columns for data extraction:

```bash
curl -X POST http://localhost:3000/api/columns/update \
  -H "Content-Type: application/json" \
  -d '{"columns": ["CustomerName", "OrderNumber", "Amount", "Priority"]}'
```

## Monitoring and Logs

- **Console Logs**: Real-time processing information
- **File Logs**:
  - `logs/combined.log` - All logs
  - `logs/error.log` - Error logs only
- **Statistics**: `GET /api/stats` for processing metrics

## Required APIs and Access

### Google Cloud Console
1. **Enable APIs**:
   - Gmail API
   - Google Sheets API
   - Google Drive API (for sheets access)

2. **OAuth 2.0 Setup**:
   - Create OAuth 2.0 Client ID
   - Add authorized redirect URI: `http://localhost:3000/auth/google/callback`
   - Download client secret

3. **Scopes Required**:
   - `https://www.googleapis.com/auth/gmail.readonly`
   - `https://www.googleapis.com/auth/gmail.modify`
   - `https://www.googleapis.com/auth/spreadsheets`

### OpenRouter
1. Create account at [OpenRouter](https://openrouter.ai)
2. Generate API key
3. Ensure access to Kimi K2 model (`moonshot/kimi-k2`)

## Security Notes

- Store credentials securely in production
- Use HTTPS in production environments
- Implement proper token storage and refresh mechanisms
- Consider implementing webhook signature verification
- Regular security audits recommended

## Troubleshooting

### Common Issues
1. **Authentication Failed**: Check client secret and redirect URI
2. **API Quota Exceeded**: Monitor Gmail API usage limits
3. **Sheets Access Denied**: Verify spreadsheet sharing permissions
4. **OpenRouter Errors**: Check API key and model availability

### Debug Mode
Set `LOG_LEVEL=debug` in `.env` for detailed logging.

## Production Deployment

For production deployment:
1. Set `NODE_ENV=production`
2. Use process manager (PM2, Docker)
3. Implement proper secret management
4. Setup monitoring and alerting
5. Configure webhook endpoints with HTTPS
6. Implement database for token storage

## Support

For issues and questions:
- Check logs in `logs/` directory
- Use `GET /api/health` for system status
- Monitor processing statistics with `GET /api/stats`
