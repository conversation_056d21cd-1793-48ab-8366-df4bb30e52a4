const { google } = require('googleapis');
const config = require('../config/config');
const logger = require('../utils/logger');

class OAuthHandler {
  constructor() {
    this.oauth2Client = new google.auth.OAuth2(
      config.google.clientId,
      config.google.clientSecret,
      config.google.redirectUri
    );

    this.tokens = null;
  }

  /**
   * Generate authorization URL for OAuth flow
   */
  getAuthUrl() {
    const authUrl = this.oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: config.google.scopes,
      prompt: 'consent'
    });

    logger.info('Generated OAuth URL');
    return authUrl;
  }

  /**
   * Exchange authorization code for tokens
   */
  async getTokens(code) {
    try {
      const { tokens } = await this.oauth2Client.getToken(code);
      this.oauth2Client.setCredentials(tokens);
      this.tokens = tokens;

      logger.info('Successfully obtained OAuth tokens');
      return tokens;
    } catch (error) {
      logger.error('Error getting tokens:', error);
      throw new Error('Failed to obtain OAuth tokens');
    }
  }

  /**
   * Set stored tokens
   */
  setTokens(tokens) {
    this.tokens = tokens;
    this.oauth2Client.setCredentials(tokens);
    logger.info('OAuth tokens set successfully');
  }

  /**
   * Refresh access token if needed
   */
  async refreshTokenIfNeeded() {
    try {
      if (!this.tokens) {
        throw new Error('No tokens available');
      }

      // Check if token is expired or will expire soon (within 5 minutes)
      const expiryDate = new Date(this.tokens.expiry_date);
      const now = new Date();
      const fiveMinutesFromNow = new Date(now.getTime() + 5 * 60 * 1000);

      if (expiryDate <= fiveMinutesFromNow) {
        logger.info('Refreshing OAuth token');
        const { credentials } = await this.oauth2Client.refreshAccessToken();
        this.tokens = credentials;
        this.oauth2Client.setCredentials(credentials);
        logger.info('OAuth token refreshed successfully');
      }

      return this.tokens;
    } catch (error) {
      logger.error('Error refreshing token:', error);
      throw new Error('Failed to refresh OAuth token');
    }
  }

  /**
   * Get authenticated Gmail client
   */
  getGmailClient() {
    if (!this.tokens) {
      throw new Error('No OAuth tokens available. Please authenticate first.');
    }

    return google.gmail({ version: 'v1', auth: this.oauth2Client });
  }

  /**
   * Get authenticated Sheets client
   */
  getSheetsClient() {
    if (!this.tokens) {
      throw new Error('No OAuth tokens available. Please authenticate first.');
    }

    return google.sheets({ version: 'v4', auth: this.oauth2Client });
  }

  /**
   * Get authenticated Drive client
   */
  getDriveClient() {
    if (!this.tokens) {
      throw new Error('No OAuth tokens available. Please authenticate first.');
    }

    return google.drive({ version: 'v3', auth: this.oauth2Client });
  }

  /**
   * Check if authenticated
   */
  isAuthenticated() {
    return this.tokens !== null;
  }
}

module.exports = OAuthHandler;
