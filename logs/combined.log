{"level":"info","message":"Email monitoring scheduled (every minute)","service":"praatika-agent","timestamp":"2025-08-01T19:24:59.868Z"}
{"level":"info","message":"Praatika Agent initialized successfully","service":"praatika-agent","timestamp":"2025-08-01T19:24:59.871Z"}
{"level":"info","message":"Praatika Agent running on port 3000","service":"praatika-agent","timestamp":"2025-08-01T19:24:59.872Z"}
{"level":"info","message":"Environment: development","service":"praatika-agent","timestamp":"2025-08-01T19:24:59.872Z"}
{"level":"info","message":"Available endpoints:","service":"praatika-agent","timestamp":"2025-08-01T19:24:59.872Z"}
{"level":"info","message":"  GET  / - Status and info","service":"praatika-agent","timestamp":"2025-08-01T19:24:59.872Z"}
{"level":"info","message":"  GET  /auth/google - Start OAuth flow","service":"praatika-agent","timestamp":"2025-08-01T19:24:59.872Z"}
{"level":"info","message":"  GET  /auth/status - Check authentication","service":"praatika-agent","timestamp":"2025-08-01T19:24:59.873Z"}
{"level":"info","message":"  GET  /api/health - Health check","service":"praatika-agent","timestamp":"2025-08-01T19:24:59.873Z"}
{"level":"info","message":"  GET  /api/emails/check - Manual email check","service":"praatika-agent","timestamp":"2025-08-01T19:24:59.873Z"}
{"level":"info","message":"  GET  /api/stats - Processing statistics","service":"praatika-agent","timestamp":"2025-08-01T19:24:59.873Z"}
{"level":"info","message":"SIGINT received","service":"praatika-agent","timestamp":"2025-08-01T19:25:45.401Z"}
