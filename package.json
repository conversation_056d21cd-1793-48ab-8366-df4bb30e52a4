{"name": "pra<PERSON><PERSON>-email-agent", "version": "1.0.0", "description": "AI-powered email automation agent for Gmail to Google Sheets integration", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "node test-setup.js", "setup": "node test-setup.js"}, "keywords": ["email", "automation", "ai", "gmail", "google-sheets"], "author": "Praatika Agent", "license": "MIT", "dependencies": {"express": "^4.18.2", "googleapis": "^128.0.0", "axios": "^1.6.0", "dotenv": "^16.3.1", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "node-cron": "^3.0.3", "winston": "^3.11.0", "joi": "^17.11.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}