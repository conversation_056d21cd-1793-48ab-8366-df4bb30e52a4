/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace tpu_v1 {
    export interface Options extends GlobalOptions {
        version: 'v1';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Cloud TPU API
     *
     * TPU API provides customers with access to Google TPU technology.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const tpu = google.tpu('v1');
     * ```
     */
    export class Tpu {
        context: APIRequestContext;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * A accelerator type that a Node can be configured with.
     */
    export interface Schema$AcceleratorType {
        /**
         * The resource name.
         */
        name?: string | null;
        /**
         * the accelerator type.
         */
        type?: string | null;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * Response for ListAcceleratorTypes.
     */
    export interface Schema$ListAcceleratorTypesResponse {
        /**
         * The listed nodes.
         */
        acceleratorTypes?: Schema$AcceleratorType[];
        /**
         * The next page token or empty if none.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * The response message for Locations.ListLocations.
     */
    export interface Schema$ListLocationsResponse {
        /**
         * A list of locations that matches the specified filter in the request.
         */
        locations?: Schema$Location[];
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response for ListNodes.
     */
    export interface Schema$ListNodesResponse {
        /**
         * The next page token or empty if none.
         */
        nextPageToken?: string | null;
        /**
         * The listed nodes.
         */
        nodes?: Schema$Node[];
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * The response message for Operations.ListOperations.
     */
    export interface Schema$ListOperationsResponse {
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
        /**
         * A list of operations that matches the specified filter in the request.
         */
        operations?: Schema$Operation[];
    }
    /**
     * Response for ListTensorFlowVersions.
     */
    export interface Schema$ListTensorFlowVersionsResponse {
        /**
         * The next page token or empty if none.
         */
        nextPageToken?: string | null;
        /**
         * The listed nodes.
         */
        tensorflowVersions?: Schema$TensorFlowVersion[];
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * A resource that represents a Google Cloud location.
     */
    export interface Schema$Location {
        /**
         * The friendly name for this location, typically a nearby city name. For example, "Tokyo".
         */
        displayName?: string | null;
        /**
         * Cross-service attributes for the location. For example {"cloud.googleapis.com/region": "us-east1"\}
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The canonical id for this location. For example: `"us-east1"`.
         */
        locationId?: string | null;
        /**
         * Service-specific metadata. For example the available capacity at the given location.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * Resource name for the location, which may vary between implementations. For example: `"projects/example-project/locations/us-east1"`
         */
        name?: string | null;
    }
    /**
     * A network endpoint over which a TPU worker can be reached.
     */
    export interface Schema$NetworkEndpoint {
        /**
         * The IP address of this network endpoint.
         */
        ipAddress?: string | null;
        /**
         * The port of this network endpoint.
         */
        port?: number | null;
    }
    /**
     * A TPU instance.
     */
    export interface Schema$Node {
        /**
         * Required. The type of hardware accelerators associated with this node.
         */
        acceleratorType?: string | null;
        /**
         * Output only. The API version that created this Node.
         */
        apiVersion?: string | null;
        /**
         * The CIDR block that the TPU node will use when selecting an IP address. This CIDR block must be a /29 block; the Compute Engine networks API forbids a smaller block, and using a larger block would be wasteful (a node can only consume one IP address). Errors will occur if the CIDR block has already been used for a currently existing TPU node, the CIDR block conflicts with any subnetworks in the user's provided network, or the provided network is peered with another network that is using that CIDR block.
         */
        cidrBlock?: string | null;
        /**
         * Output only. The time when the node was created.
         */
        createTime?: string | null;
        /**
         * The user-supplied description of the TPU. Maximum of 512 characters.
         */
        description?: string | null;
        /**
         * The health status of the TPU node.
         */
        health?: string | null;
        /**
         * Output only. If this field is populated, it contains a description of why the TPU Node is unhealthy.
         */
        healthDescription?: string | null;
        /**
         * Output only. DEPRECATED! Use network_endpoints instead. The network address for the TPU Node as visible to Compute Engine instances.
         */
        ipAddress?: string | null;
        /**
         * Resource labels to represent user-provided metadata.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. Immutable. The name of the TPU
         */
        name?: string | null;
        /**
         * The name of a network they wish to peer the TPU node to. It must be a preexisting Compute Engine network inside of the project on which this API has been activated. If none is provided, "default" will be used.
         */
        network?: string | null;
        /**
         * Output only. The network endpoints where TPU workers can be accessed and sent work. It is recommended that Tensorflow clients of the node reach out to the 0th entry in this map first.
         */
        networkEndpoints?: Schema$NetworkEndpoint[];
        /**
         * Output only. DEPRECATED! Use network_endpoints instead. The network port for the TPU Node as visible to Compute Engine instances.
         */
        port?: string | null;
        /**
         * The scheduling options for this node.
         */
        schedulingConfig?: Schema$SchedulingConfig;
        /**
         * Output only. The service account used to run the tensor flow services within the node. To share resources, including Google Cloud Storage data, with the Tensorflow job running in the Node, this account must have permissions to that data.
         */
        serviceAccount?: string | null;
        /**
         * Output only. The current state for the TPU Node.
         */
        state?: string | null;
        /**
         * Output only. The Symptoms that have occurred to the TPU Node.
         */
        symptoms?: Schema$Symptom[];
        /**
         * Required. The version of Tensorflow running in the Node.
         */
        tensorflowVersion?: string | null;
        /**
         * Whether the VPC peering for the node is set up through Service Networking API. The VPC Peering should be set up before provisioning the node. If this field is set, cidr_block field should not be specified. If the network, that you want to peer the TPU Node to, is Shared VPC networks, the node must be created with this this field enabled.
         */
        useServiceNetworking?: boolean | null;
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$Operation {
        /**
         * If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$Status;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id\}`.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * Metadata describing an Operation
     */
    export interface Schema$OperationMetadata {
        /**
         * API version.
         */
        apiVersion?: string | null;
        /**
         * Specifies if cancellation was requested for the operation.
         */
        cancelRequested?: boolean | null;
        /**
         * The time the operation was created.
         */
        createTime?: string | null;
        /**
         * The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Human-readable status of the operation, if any.
         */
        statusDetail?: string | null;
        /**
         * Target of the operation - for example projects/project-1/connectivityTests/test-1
         */
        target?: string | null;
        /**
         * Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * Request for ReimageNode.
     */
    export interface Schema$ReimageNodeRequest {
        /**
         * The version for reimage to create.
         */
        tensorflowVersion?: string | null;
    }
    /**
     * Sets the scheduling options for this node.
     */
    export interface Schema$SchedulingConfig {
        /**
         * Defines whether the node is preemptible.
         */
        preemptible?: boolean | null;
        /**
         * Whether the node is created under a reservation.
         */
        reserved?: boolean | null;
    }
    /**
     * Request for StartNode.
     */
    export interface Schema$StartNodeRequest {
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$Status {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    /**
     * Request for StopNode.
     */
    export interface Schema$StopNodeRequest {
    }
    /**
     * A Symptom instance.
     */
    export interface Schema$Symptom {
        /**
         * Timestamp when the Symptom is created.
         */
        createTime?: string | null;
        /**
         * Detailed information of the current Symptom.
         */
        details?: string | null;
        /**
         * Type of the Symptom.
         */
        symptomType?: string | null;
        /**
         * A string used to uniquely distinguish a worker within a TPU node.
         */
        workerId?: string | null;
    }
    /**
     * A tensorflow version that a Node can be configured with.
     */
    export interface Schema$TensorFlowVersion {
        /**
         * The resource name.
         */
        name?: string | null;
        /**
         * the tensorflow version.
         */
        version?: string | null;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        locations: Resource$Projects$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations {
        context: APIRequestContext;
        acceleratorTypes: Resource$Projects$Locations$Acceleratortypes;
        nodes: Resource$Projects$Locations$Nodes;
        operations: Resource$Projects$Locations$Operations;
        tensorflowVersions: Resource$Projects$Locations$Tensorflowversions;
        constructor(context: APIRequestContext);
        /**
         * Gets information about a location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Get, options?: MethodOptions): GaxiosPromise<Schema$Location>;
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Get, options: MethodOptions | BodyResponseCallback<Schema$Location>, callback: BodyResponseCallback<Schema$Location>): void;
        get(params: Params$Resource$Projects$Locations$Get, callback: BodyResponseCallback<Schema$Location>): void;
        get(callback: BodyResponseCallback<Schema$Location>): void;
        /**
         * Lists information about the supported locations for this service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$List, options?: MethodOptions): GaxiosPromise<Schema$ListLocationsResponse>;
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$List, options: MethodOptions | BodyResponseCallback<Schema$ListLocationsResponse>, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$List, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Get extends StandardParameters {
        /**
         * Resource name for the location.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$List extends StandardParameters {
        /**
         * A filter to narrow down results to a preferred subset. The filtering language accepts strings like `"displayName=tokyo"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).
         */
        filter?: string;
        /**
         * The resource that owns the locations collection, if applicable.
         */
        name?: string;
        /**
         * The maximum number of results to return. If not set, the service selects a default.
         */
        pageSize?: number;
        /**
         * A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.
         */
        pageToken?: string;
    }
    export class Resource$Projects$Locations$Acceleratortypes {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets AcceleratorType.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Acceleratortypes$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Acceleratortypes$Get, options?: MethodOptions): GaxiosPromise<Schema$AcceleratorType>;
        get(params: Params$Resource$Projects$Locations$Acceleratortypes$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Acceleratortypes$Get, options: MethodOptions | BodyResponseCallback<Schema$AcceleratorType>, callback: BodyResponseCallback<Schema$AcceleratorType>): void;
        get(params: Params$Resource$Projects$Locations$Acceleratortypes$Get, callback: BodyResponseCallback<Schema$AcceleratorType>): void;
        get(callback: BodyResponseCallback<Schema$AcceleratorType>): void;
        /**
         * Lists accelerator types supported by this API.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Acceleratortypes$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Acceleratortypes$List, options?: MethodOptions): GaxiosPromise<Schema$ListAcceleratorTypesResponse>;
        list(params: Params$Resource$Projects$Locations$Acceleratortypes$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Acceleratortypes$List, options: MethodOptions | BodyResponseCallback<Schema$ListAcceleratorTypesResponse>, callback: BodyResponseCallback<Schema$ListAcceleratorTypesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Acceleratortypes$List, callback: BodyResponseCallback<Schema$ListAcceleratorTypesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListAcceleratorTypesResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Acceleratortypes$Get extends StandardParameters {
        /**
         * Required. The resource name.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Acceleratortypes$List extends StandardParameters {
        /**
         * List filter.
         */
        filter?: string;
        /**
         * Sort results.
         */
        orderBy?: string;
        /**
         * The maximum number of items to return.
         */
        pageSize?: number;
        /**
         * The next_page_token value returned from a previous List request, if any.
         */
        pageToken?: string;
        /**
         * Required. The parent resource name.
         */
        parent?: string;
    }
    export class Resource$Projects$Locations$Nodes {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a node.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Nodes$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Nodes$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Projects$Locations$Nodes$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Nodes$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Nodes$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a node.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Nodes$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Nodes$Delete, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        delete(params: Params$Resource$Projects$Locations$Nodes$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Nodes$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Nodes$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets the details of a node.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Nodes$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Nodes$Get, options?: MethodOptions): GaxiosPromise<Schema$Node>;
        get(params: Params$Resource$Projects$Locations$Nodes$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Nodes$Get, options: MethodOptions | BodyResponseCallback<Schema$Node>, callback: BodyResponseCallback<Schema$Node>): void;
        get(params: Params$Resource$Projects$Locations$Nodes$Get, callback: BodyResponseCallback<Schema$Node>): void;
        get(callback: BodyResponseCallback<Schema$Node>): void;
        /**
         * Lists nodes.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Nodes$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Nodes$List, options?: MethodOptions): GaxiosPromise<Schema$ListNodesResponse>;
        list(params: Params$Resource$Projects$Locations$Nodes$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Nodes$List, options: MethodOptions | BodyResponseCallback<Schema$ListNodesResponse>, callback: BodyResponseCallback<Schema$ListNodesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Nodes$List, callback: BodyResponseCallback<Schema$ListNodesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListNodesResponse>): void;
        /**
         * Reimages a node's OS.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        reimage(params: Params$Resource$Projects$Locations$Nodes$Reimage, options: StreamMethodOptions): GaxiosPromise<Readable>;
        reimage(params?: Params$Resource$Projects$Locations$Nodes$Reimage, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        reimage(params: Params$Resource$Projects$Locations$Nodes$Reimage, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        reimage(params: Params$Resource$Projects$Locations$Nodes$Reimage, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        reimage(params: Params$Resource$Projects$Locations$Nodes$Reimage, callback: BodyResponseCallback<Schema$Operation>): void;
        reimage(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Starts a node.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        start(params: Params$Resource$Projects$Locations$Nodes$Start, options: StreamMethodOptions): GaxiosPromise<Readable>;
        start(params?: Params$Resource$Projects$Locations$Nodes$Start, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        start(params: Params$Resource$Projects$Locations$Nodes$Start, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        start(params: Params$Resource$Projects$Locations$Nodes$Start, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        start(params: Params$Resource$Projects$Locations$Nodes$Start, callback: BodyResponseCallback<Schema$Operation>): void;
        start(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Stops a node, this operation is only available with single TPU nodes.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        stop(params: Params$Resource$Projects$Locations$Nodes$Stop, options: StreamMethodOptions): GaxiosPromise<Readable>;
        stop(params?: Params$Resource$Projects$Locations$Nodes$Stop, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        stop(params: Params$Resource$Projects$Locations$Nodes$Stop, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        stop(params: Params$Resource$Projects$Locations$Nodes$Stop, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        stop(params: Params$Resource$Projects$Locations$Nodes$Stop, callback: BodyResponseCallback<Schema$Operation>): void;
        stop(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Nodes$Create extends StandardParameters {
        /**
         * The unqualified resource name.
         */
        nodeId?: string;
        /**
         * Required. The parent resource name.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Node;
    }
    export interface Params$Resource$Projects$Locations$Nodes$Delete extends StandardParameters {
        /**
         * Required. The resource name.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Nodes$Get extends StandardParameters {
        /**
         * Required. The resource name.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Nodes$List extends StandardParameters {
        /**
         * The maximum number of items to return.
         */
        pageSize?: number;
        /**
         * The next_page_token value returned from a previous List request, if any.
         */
        pageToken?: string;
        /**
         * Required. The parent resource name.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Nodes$Reimage extends StandardParameters {
        /**
         * The resource name.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ReimageNodeRequest;
    }
    export interface Params$Resource$Projects$Locations$Nodes$Start extends StandardParameters {
        /**
         * The resource name.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$StartNodeRequest;
    }
    export interface Params$Resource$Projects$Locations$Nodes$Stop extends StandardParameters {
        /**
         * The resource name.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$StopNodeRequest;
    }
    export class Resource$Projects$Locations$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions): GaxiosPromise<Readable>;
        cancel(params?: Params$Resource$Projects$Locations$Operations$Cancel, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Operations$Delete, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Operations$Get, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, callback: BodyResponseCallback<Schema$Operation>): void;
        get(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Operations$List, options?: MethodOptions): GaxiosPromise<Schema$ListOperationsResponse>;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$ListOperationsResponse>, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Operations$Cancel extends StandardParameters {
        /**
         * The name of the operation resource to be cancelled.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$Delete extends StandardParameters {
        /**
         * The name of the operation resource to be deleted.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export class Resource$Projects$Locations$Tensorflowversions {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets TensorFlow Version.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Tensorflowversions$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Tensorflowversions$Get, options?: MethodOptions): GaxiosPromise<Schema$TensorFlowVersion>;
        get(params: Params$Resource$Projects$Locations$Tensorflowversions$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Tensorflowversions$Get, options: MethodOptions | BodyResponseCallback<Schema$TensorFlowVersion>, callback: BodyResponseCallback<Schema$TensorFlowVersion>): void;
        get(params: Params$Resource$Projects$Locations$Tensorflowversions$Get, callback: BodyResponseCallback<Schema$TensorFlowVersion>): void;
        get(callback: BodyResponseCallback<Schema$TensorFlowVersion>): void;
        /**
         * List TensorFlow versions supported by this API.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Tensorflowversions$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Tensorflowversions$List, options?: MethodOptions): GaxiosPromise<Schema$ListTensorFlowVersionsResponse>;
        list(params: Params$Resource$Projects$Locations$Tensorflowversions$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Tensorflowversions$List, options: MethodOptions | BodyResponseCallback<Schema$ListTensorFlowVersionsResponse>, callback: BodyResponseCallback<Schema$ListTensorFlowVersionsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Tensorflowversions$List, callback: BodyResponseCallback<Schema$ListTensorFlowVersionsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListTensorFlowVersionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Tensorflowversions$Get extends StandardParameters {
        /**
         * Required. The resource name.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Tensorflowversions$List extends StandardParameters {
        /**
         * List filter.
         */
        filter?: string;
        /**
         * Sort results.
         */
        orderBy?: string;
        /**
         * The maximum number of items to return.
         */
        pageSize?: number;
        /**
         * The next_page_token value returned from a previous List request, if any.
         */
        pageToken?: string;
        /**
         * Required. The parent resource name.
         */
        parent?: string;
    }
    export {};
}
