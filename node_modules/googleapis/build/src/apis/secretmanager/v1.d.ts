/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace secretmanager_v1 {
    export interface Options extends GlobalOptions {
        version: 'v1';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Secret Manager API
     *
     * Stores sensitive data such as API keys, passwords, and certificates. Provides convenience while improving security.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const secretmanager = google.secretmanager('v1');
     * ```
     */
    export class Secretmanager {
        context: APIRequestContext;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Response message for SecretManagerService.AccessSecretVersion.
     */
    export interface Schema$AccessSecretVersionResponse {
        /**
         * The resource name of the SecretVersion in the format `projects/x/secrets/x/versions/x`.
         */
        name?: string | null;
        /**
         * Secret payload
         */
        payload?: Schema$SecretPayload;
    }
    /**
     * Request message for SecretManagerService.AddSecretVersion.
     */
    export interface Schema$AddSecretVersionRequest {
        /**
         * Required. The secret payload of the SecretVersion.
         */
        payload?: Schema$SecretPayload;
    }
    /**
     * Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { "audit_configs": [ { "service": "allServices", "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \}, { "log_type": "ADMIN_READ" \} ] \}, { "service": "sampleservice.googleapis.com", "audit_log_configs": [ { "log_type": "DATA_READ" \}, { "log_type": "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] \} ] \} ] \} For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.
     */
    export interface Schema$AuditConfig {
        /**
         * The configuration for logging of each type of permission.
         */
        auditLogConfigs?: Schema$AuditLogConfig[];
        /**
         * Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.
         */
        service?: string | null;
    }
    /**
     * Provides the configuration for logging a type of permissions. Example: { "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \} ] \} This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.
     */
    export interface Schema$AuditLogConfig {
        /**
         * Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.
         */
        exemptedMembers?: string[] | null;
        /**
         * The log type that this config enables.
         */
        logType?: string | null;
    }
    /**
     * A replication policy that replicates the Secret payload without any restrictions.
     */
    export interface Schema$Automatic {
        /**
         * Optional. The customer-managed encryption configuration of the Secret. If no configuration is provided, Google-managed default encryption is used. Updates to the Secret encryption configuration only apply to SecretVersions added afterwards. They do not apply retroactively to existing SecretVersions.
         */
        customerManagedEncryption?: Schema$CustomerManagedEncryption;
    }
    /**
     * The replication status of a SecretVersion using automatic replication. Only populated if the parent Secret has an automatic replication policy.
     */
    export interface Schema$AutomaticStatus {
        /**
         * Output only. The customer-managed encryption status of the SecretVersion. Only populated if customer-managed encryption is used.
         */
        customerManagedEncryption?: Schema$CustomerManagedEncryptionStatus;
    }
    /**
     * Associates `members`, or principals, with a `role`.
     */
    export interface Schema$Binding {
        /**
         * The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        condition?: Schema$Expr;
        /**
         * Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid\}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid\}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid\}.svc.id.goog[{namespace\}/{kubernetes-sa\}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid\}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain\}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `deleted:user:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid\}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid\}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid\}` and the recovered group retains the role in the binding.
         */
        members?: string[] | null;
        /**
         * Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`.
         */
        role?: string | null;
    }
    /**
     * Configuration for encrypting secret payloads using customer-managed encryption keys (CMEK).
     */
    export interface Schema$CustomerManagedEncryption {
        /**
         * Required. The resource name of the Cloud KMS CryptoKey used to encrypt secret payloads. For secrets using the UserManaged replication policy type, Cloud KMS CryptoKeys must reside in the same location as the replica location. For secrets using the Automatic replication policy type, Cloud KMS CryptoKeys must reside in `global`. The expected format is `projects/x/locations/x/keyRings/x/cryptoKeys/x`.
         */
        kmsKeyName?: string | null;
    }
    /**
     * Describes the status of customer-managed encryption.
     */
    export interface Schema$CustomerManagedEncryptionStatus {
        /**
         * Required. The resource name of the Cloud KMS CryptoKeyVersion used to encrypt the secret payload, in the following format: `projects/x/locations/x/keyRings/x/cryptoKeys/x/versions/x`.
         */
        kmsKeyVersionName?: string | null;
    }
    /**
     * Request message for SecretManagerService.DestroySecretVersion.
     */
    export interface Schema$DestroySecretVersionRequest {
        /**
         * Optional. Etag of the SecretVersion. The request succeeds if it matches the etag of the currently stored secret version object. If the etag is omitted, the request succeeds.
         */
        etag?: string | null;
    }
    /**
     * Request message for SecretManagerService.DisableSecretVersion.
     */
    export interface Schema$DisableSecretVersionRequest {
        /**
         * Optional. Etag of the SecretVersion. The request succeeds if it matches the etag of the currently stored secret version object. If the etag is omitted, the request succeeds.
         */
        etag?: string | null;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * Request message for SecretManagerService.EnableSecretVersion.
     */
    export interface Schema$EnableSecretVersionRequest {
        /**
         * Optional. Etag of the SecretVersion. The request succeeds if it matches the etag of the currently stored secret version object. If the etag is omitted, the request succeeds.
         */
        etag?: string | null;
    }
    /**
     * Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: "Summary size limit" description: "Determines if a summary is less than 100 chars" expression: "document.summary.size() < 100" Example (Equality): title: "Requestor is owner" description: "Determines if requestor is the document owner" expression: "document.owner == request.auth.claims.email" Example (Logic): title: "Public documents" description: "Determine whether the document should be publicly visible" expression: "document.type != 'private' && document.type != 'internal'" Example (Data Manipulation): title: "Notification string" description: "Create a notification string with a timestamp." expression: "'New message received at ' + string(document.create_time)" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.
     */
    export interface Schema$Expr {
        /**
         * Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.
         */
        description?: string | null;
        /**
         * Textual representation of an expression in Common Expression Language syntax.
         */
        expression?: string | null;
        /**
         * Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.
         */
        location?: string | null;
        /**
         * Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.
         */
        title?: string | null;
    }
    /**
     * The response message for Locations.ListLocations.
     */
    export interface Schema$ListLocationsResponse {
        /**
         * A list of locations that matches the specified filter in the request.
         */
        locations?: Schema$Location[];
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response message for SecretManagerService.ListSecrets.
     */
    export interface Schema$ListSecretsResponse {
        /**
         * A token to retrieve the next page of results. Pass this value in ListSecretsRequest.page_token to retrieve the next page.
         */
        nextPageToken?: string | null;
        /**
         * The list of Secrets sorted in reverse by create_time (newest first).
         */
        secrets?: Schema$Secret[];
        /**
         * The total number of Secrets but 0 when the ListSecretsRequest.filter field is set.
         */
        totalSize?: number | null;
    }
    /**
     * Response message for SecretManagerService.ListSecretVersions.
     */
    export interface Schema$ListSecretVersionsResponse {
        /**
         * A token to retrieve the next page of results. Pass this value in ListSecretVersionsRequest.page_token to retrieve the next page.
         */
        nextPageToken?: string | null;
        /**
         * The total number of SecretVersions but 0 when the ListSecretsRequest.filter field is set.
         */
        totalSize?: number | null;
        /**
         * The list of SecretVersions sorted in reverse by create_time (newest first).
         */
        versions?: Schema$SecretVersion[];
    }
    /**
     * A resource that represents a Google Cloud location.
     */
    export interface Schema$Location {
        /**
         * The friendly name for this location, typically a nearby city name. For example, "Tokyo".
         */
        displayName?: string | null;
        /**
         * Cross-service attributes for the location. For example {"cloud.googleapis.com/region": "us-east1"\}
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The canonical id for this location. For example: `"us-east1"`.
         */
        locationId?: string | null;
        /**
         * Service-specific metadata. For example the available capacity at the given location.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * Resource name for the location, which may vary between implementations. For example: `"projects/example-project/locations/us-east1"`
         */
        name?: string | null;
    }
    /**
     * An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { "bindings": [ { "role": "roles/resourcemanager.organizationAdmin", "members": [ "user:<EMAIL>", "group:<EMAIL>", "domain:google.com", "serviceAccount:<EMAIL>" ] \}, { "role": "roles/resourcemanager.organizationViewer", "members": [ "user:<EMAIL>" ], "condition": { "title": "expirable access", "description": "Does not grant access after Sep 2020", "expression": "request.time < timestamp('2020-10-01T00:00:00.000Z')", \} \} ], "etag": "BwWWja0YfJA=", "version": 3 \} ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).
     */
    export interface Schema$Policy {
        /**
         * Specifies cloud audit logging configuration for this policy.
         */
        auditConfigs?: Schema$AuditConfig[];
        /**
         * Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.
         */
        bindings?: Schema$Binding[];
        /**
         * `etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.
         */
        etag?: string | null;
        /**
         * Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        version?: number | null;
    }
    /**
     * Represents a Replica for this Secret.
     */
    export interface Schema$Replica {
        /**
         * Optional. The customer-managed encryption configuration of the User-Managed Replica. If no configuration is provided, Google-managed default encryption is used. Updates to the Secret encryption configuration only apply to SecretVersions added afterwards. They do not apply retroactively to existing SecretVersions.
         */
        customerManagedEncryption?: Schema$CustomerManagedEncryption;
        /**
         * The canonical IDs of the location to replicate data. For example: `"us-east1"`.
         */
        location?: string | null;
    }
    /**
     * Describes the status of a user-managed replica for the SecretVersion.
     */
    export interface Schema$ReplicaStatus {
        /**
         * Output only. The customer-managed encryption status of the SecretVersion. Only populated if customer-managed encryption is used.
         */
        customerManagedEncryption?: Schema$CustomerManagedEncryptionStatus;
        /**
         * Output only. The canonical ID of the replica location. For example: `"us-east1"`.
         */
        location?: string | null;
    }
    /**
     * A policy that defines the replication and encryption configuration of data.
     */
    export interface Schema$Replication {
        /**
         * The Secret will automatically be replicated without any restrictions.
         */
        automatic?: Schema$Automatic;
        /**
         * The Secret will only be replicated into the locations specified.
         */
        userManaged?: Schema$UserManaged;
    }
    /**
     * The replication status of a SecretVersion.
     */
    export interface Schema$ReplicationStatus {
        /**
         * Describes the replication status of a SecretVersion with automatic replication. Only populated if the parent Secret has an automatic replication policy.
         */
        automatic?: Schema$AutomaticStatus;
        /**
         * Describes the replication status of a SecretVersion with user-managed replication. Only populated if the parent Secret has a user-managed replication policy.
         */
        userManaged?: Schema$UserManagedStatus;
    }
    /**
     * The rotation time and period for a Secret. At next_rotation_time, Secret Manager will send a Pub/Sub notification to the topics configured on the Secret. Secret.topics must be set to configure rotation.
     */
    export interface Schema$Rotation {
        /**
         * Optional. Timestamp in UTC at which the Secret is scheduled to rotate. Cannot be set to less than 300s (5 min) in the future and at most 3153600000s (100 years). next_rotation_time MUST be set if rotation_period is set.
         */
        nextRotationTime?: string | null;
        /**
         * Input only. The Duration between rotation notifications. Must be in seconds and at least 3600s (1h) and at most 3153600000s (100 years). If rotation_period is set, next_rotation_time must be set. next_rotation_time will be advanced by this period when the service automatically sends rotation notifications.
         */
        rotationPeriod?: string | null;
    }
    /**
     * A Secret is a logical secret whose value and versions can be accessed. A Secret is made up of zero or more SecretVersions that represent the secret data.
     */
    export interface Schema$Secret {
        /**
         * Optional. Custom metadata about the secret. Annotations are distinct from various forms of labels. Annotations exist to allow client tools to store their own state information without requiring a database. Annotation keys must be between 1 and 63 characters long, have a UTF-8 encoding of maximum 128 bytes, begin and end with an alphanumeric character ([a-z0-9A-Z]), and may have dashes (-), underscores (_), dots (.), and alphanumerics in between these symbols. The total size of annotation keys and values must be less than 16KiB.
         */
        annotations?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. The time at which the Secret was created.
         */
        createTime?: string | null;
        /**
         * Optional. Etag of the currently stored Secret.
         */
        etag?: string | null;
        /**
         * Optional. Timestamp in UTC when the Secret is scheduled to expire. This is always provided on output, regardless of what was sent on input.
         */
        expireTime?: string | null;
        /**
         * The labels assigned to this Secret. Label keys must be between 1 and 63 characters long, have a UTF-8 encoding of maximum 128 bytes, and must conform to the following PCRE regular expression: `\p{Ll\}\p{Lo\}{0,62\}` Label values must be between 0 and 63 characters long, have a UTF-8 encoding of maximum 128 bytes, and must conform to the following PCRE regular expression: `[\p{Ll\}\p{Lo\}\p{N\}_-]{0,63\}` No more than 64 labels can be assigned to a given resource.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. The resource name of the Secret in the format `projects/x/secrets/x`.
         */
        name?: string | null;
        /**
         * Required. Immutable. The replication policy of the secret data attached to the Secret. The replication policy cannot be changed after the Secret has been created.
         */
        replication?: Schema$Replication;
        /**
         * Optional. Rotation policy attached to the Secret. May be excluded if there is no rotation policy.
         */
        rotation?: Schema$Rotation;
        /**
         * Optional. A list of up to 10 Pub/Sub topics to which messages are published when control plane operations are called on the secret or its versions.
         */
        topics?: Schema$Topic[];
        /**
         * Input only. The TTL for the Secret.
         */
        ttl?: string | null;
        /**
         * Optional. Mapping from version alias to version name. A version alias is a string with a maximum length of 63 characters and can contain uppercase and lowercase letters, numerals, and the hyphen (`-`) and underscore ('_') characters. An alias string must start with a letter and cannot be the string 'latest' or 'NEW'. No more than 50 aliases can be assigned to a given secret. Version-Alias pairs will be viewable via GetSecret and modifiable via UpdateSecret. At launch Access by Allias will only be supported on GetSecretVersion and AccessSecretVersion.
         */
        versionAliases?: {
            [key: string]: string;
        } | null;
    }
    /**
     * A secret payload resource in the Secret Manager API. This contains the sensitive secret payload that is associated with a SecretVersion.
     */
    export interface Schema$SecretPayload {
        /**
         * The secret data. Must be no larger than 64KiB.
         */
        data?: string | null;
        /**
         * Optional. If specified, SecretManagerService will verify the integrity of the received data on SecretManagerService.AddSecretVersion calls using the crc32c checksum and store it to include in future SecretManagerService.AccessSecretVersion responses. If a checksum is not provided in the SecretManagerService.AddSecretVersion request, the SecretManagerService will generate and store one for you. The CRC32C value is encoded as a Int64 for compatibility, and can be safely downconverted to uint32 in languages that support this type. https://cloud.google.com/apis/design/design_patterns#integer_types
         */
        dataCrc32c?: string | null;
    }
    /**
     * A secret version resource in the Secret Manager API.
     */
    export interface Schema$SecretVersion {
        /**
         * Output only. True if payload checksum specified in SecretPayload object has been received by SecretManagerService on SecretManagerService.AddSecretVersion.
         */
        clientSpecifiedPayloadChecksum?: boolean | null;
        /**
         * Output only. The time at which the SecretVersion was created.
         */
        createTime?: string | null;
        /**
         * Output only. The time this SecretVersion was destroyed. Only present if state is DESTROYED.
         */
        destroyTime?: string | null;
        /**
         * Output only. Etag of the currently stored SecretVersion.
         */
        etag?: string | null;
        /**
         * Output only. The resource name of the SecretVersion in the format `projects/x/secrets/x/versions/x`. SecretVersion IDs in a Secret start at 1 and are incremented for each subsequent version of the secret.
         */
        name?: string | null;
        /**
         * The replication status of the SecretVersion.
         */
        replicationStatus?: Schema$ReplicationStatus;
        /**
         * Output only. The current state of the SecretVersion.
         */
        state?: string | null;
    }
    /**
     * Request message for `SetIamPolicy` method.
     */
    export interface Schema$SetIamPolicyRequest {
        /**
         * REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them.
         */
        policy?: Schema$Policy;
        /**
         * OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: "bindings, etag"`
         */
        updateMask?: string | null;
    }
    /**
     * Request message for `TestIamPermissions` method.
     */
    export interface Schema$TestIamPermissionsRequest {
        /**
         * The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).
         */
        permissions?: string[] | null;
    }
    /**
     * Response message for `TestIamPermissions` method.
     */
    export interface Schema$TestIamPermissionsResponse {
        /**
         * A subset of `TestPermissionsRequest.permissions` that the caller is allowed.
         */
        permissions?: string[] | null;
    }
    /**
     * A Pub/Sub topic which Secret Manager will publish to when control plane events occur on this secret.
     */
    export interface Schema$Topic {
        /**
         * Required. The resource name of the Pub/Sub topic that will be published to, in the following format: `projects/x/topics/x`. For publication to succeed, the Secret Manager service agent must have the `pubsub.topic.publish` permission on the topic. The Pub/Sub Publisher role (`roles/pubsub.publisher`) includes this permission.
         */
        name?: string | null;
    }
    /**
     * A replication policy that replicates the Secret payload into the locations specified in Secret.replication.user_managed.replicas
     */
    export interface Schema$UserManaged {
        /**
         * Required. The list of Replicas for this Secret. Cannot be empty.
         */
        replicas?: Schema$Replica[];
    }
    /**
     * The replication status of a SecretVersion using user-managed replication. Only populated if the parent Secret has a user-managed replication policy.
     */
    export interface Schema$UserManagedStatus {
        /**
         * Output only. The list of replica statuses for the SecretVersion.
         */
        replicas?: Schema$ReplicaStatus[];
    }
    export class Resource$Projects {
        context: APIRequestContext;
        locations: Resource$Projects$Locations;
        secrets: Resource$Projects$Secrets;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets information about a location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Get, options?: MethodOptions): GaxiosPromise<Schema$Location>;
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Get, options: MethodOptions | BodyResponseCallback<Schema$Location>, callback: BodyResponseCallback<Schema$Location>): void;
        get(params: Params$Resource$Projects$Locations$Get, callback: BodyResponseCallback<Schema$Location>): void;
        get(callback: BodyResponseCallback<Schema$Location>): void;
        /**
         * Lists information about the supported locations for this service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$List, options?: MethodOptions): GaxiosPromise<Schema$ListLocationsResponse>;
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$List, options: MethodOptions | BodyResponseCallback<Schema$ListLocationsResponse>, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$List, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Get extends StandardParameters {
        /**
         * Resource name for the location.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$List extends StandardParameters {
        /**
         * A filter to narrow down results to a preferred subset. The filtering language accepts strings like `"displayName=tokyo"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).
         */
        filter?: string;
        /**
         * The resource that owns the locations collection, if applicable.
         */
        name?: string;
        /**
         * The maximum number of results to return. If not set, the service selects a default.
         */
        pageSize?: number;
        /**
         * A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.
         */
        pageToken?: string;
    }
    export class Resource$Projects$Secrets {
        context: APIRequestContext;
        versions: Resource$Projects$Secrets$Versions;
        constructor(context: APIRequestContext);
        /**
         * Creates a new SecretVersion containing secret data and attaches it to an existing Secret.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        addVersion(params: Params$Resource$Projects$Secrets$Addversion, options: StreamMethodOptions): GaxiosPromise<Readable>;
        addVersion(params?: Params$Resource$Projects$Secrets$Addversion, options?: MethodOptions): GaxiosPromise<Schema$SecretVersion>;
        addVersion(params: Params$Resource$Projects$Secrets$Addversion, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        addVersion(params: Params$Resource$Projects$Secrets$Addversion, options: MethodOptions | BodyResponseCallback<Schema$SecretVersion>, callback: BodyResponseCallback<Schema$SecretVersion>): void;
        addVersion(params: Params$Resource$Projects$Secrets$Addversion, callback: BodyResponseCallback<Schema$SecretVersion>): void;
        addVersion(callback: BodyResponseCallback<Schema$SecretVersion>): void;
        /**
         * Creates a new Secret containing no SecretVersions.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Secrets$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Secrets$Create, options?: MethodOptions): GaxiosPromise<Schema$Secret>;
        create(params: Params$Resource$Projects$Secrets$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Secrets$Create, options: MethodOptions | BodyResponseCallback<Schema$Secret>, callback: BodyResponseCallback<Schema$Secret>): void;
        create(params: Params$Resource$Projects$Secrets$Create, callback: BodyResponseCallback<Schema$Secret>): void;
        create(callback: BodyResponseCallback<Schema$Secret>): void;
        /**
         * Deletes a Secret.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Secrets$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Secrets$Delete, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        delete(params: Params$Resource$Projects$Secrets$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Secrets$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Projects$Secrets$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets metadata for a given Secret.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Secrets$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Secrets$Get, options?: MethodOptions): GaxiosPromise<Schema$Secret>;
        get(params: Params$Resource$Projects$Secrets$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Secrets$Get, options: MethodOptions | BodyResponseCallback<Schema$Secret>, callback: BodyResponseCallback<Schema$Secret>): void;
        get(params: Params$Resource$Projects$Secrets$Get, callback: BodyResponseCallback<Schema$Secret>): void;
        get(callback: BodyResponseCallback<Schema$Secret>): void;
        /**
         * Gets the access control policy for a secret. Returns empty policy if the secret exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Secrets$Getiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getIamPolicy(params?: Params$Resource$Projects$Secrets$Getiampolicy, options?: MethodOptions): GaxiosPromise<Schema$Policy>;
        getIamPolicy(params: Params$Resource$Projects$Secrets$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Secrets$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Secrets$Getiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Lists Secrets.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Secrets$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Secrets$List, options?: MethodOptions): GaxiosPromise<Schema$ListSecretsResponse>;
        list(params: Params$Resource$Projects$Secrets$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Secrets$List, options: MethodOptions | BodyResponseCallback<Schema$ListSecretsResponse>, callback: BodyResponseCallback<Schema$ListSecretsResponse>): void;
        list(params: Params$Resource$Projects$Secrets$List, callback: BodyResponseCallback<Schema$ListSecretsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListSecretsResponse>): void;
        /**
         * Updates metadata of an existing Secret.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Secrets$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Secrets$Patch, options?: MethodOptions): GaxiosPromise<Schema$Secret>;
        patch(params: Params$Resource$Projects$Secrets$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Secrets$Patch, options: MethodOptions | BodyResponseCallback<Schema$Secret>, callback: BodyResponseCallback<Schema$Secret>): void;
        patch(params: Params$Resource$Projects$Secrets$Patch, callback: BodyResponseCallback<Schema$Secret>): void;
        patch(callback: BodyResponseCallback<Schema$Secret>): void;
        /**
         * Sets the access control policy on the specified secret. Replaces any existing policy. Permissions on SecretVersions are enforced according to the policy set on the associated Secret.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Secrets$Setiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        setIamPolicy(params?: Params$Resource$Projects$Secrets$Setiampolicy, options?: MethodOptions): GaxiosPromise<Schema$Policy>;
        setIamPolicy(params: Params$Resource$Projects$Secrets$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Secrets$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Secrets$Setiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Returns permissions that a caller has for the specified secret. If the secret does not exist, this call returns an empty set of permissions, not a NOT_FOUND error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Secrets$Testiampermissions, options: StreamMethodOptions): GaxiosPromise<Readable>;
        testIamPermissions(params?: Params$Resource$Projects$Secrets$Testiampermissions, options?: MethodOptions): GaxiosPromise<Schema$TestIamPermissionsResponse>;
        testIamPermissions(params: Params$Resource$Projects$Secrets$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Secrets$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Secrets$Testiampermissions, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Secrets$Addversion extends StandardParameters {
        /**
         * Required. The resource name of the Secret to associate with the SecretVersion in the format `projects/x/secrets/x`.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$AddSecretVersionRequest;
    }
    export interface Params$Resource$Projects$Secrets$Create extends StandardParameters {
        /**
         * Required. The resource name of the project to associate with the Secret, in the format `projects/x`.
         */
        parent?: string;
        /**
         * Required. This must be unique within the project. A secret ID is a string with a maximum length of 255 characters and can contain uppercase and lowercase letters, numerals, and the hyphen (`-`) and underscore (`_`) characters.
         */
        secretId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Secret;
    }
    export interface Params$Resource$Projects$Secrets$Delete extends StandardParameters {
        /**
         * Optional. Etag of the Secret. The request succeeds if it matches the etag of the currently stored secret object. If the etag is omitted, the request succeeds.
         */
        etag?: string;
        /**
         * Required. The resource name of the Secret to delete in the format `projects/x/secrets/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Secrets$Get extends StandardParameters {
        /**
         * Required. The resource name of the Secret, in the format `projects/x/secrets/x`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Secrets$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Secrets$List extends StandardParameters {
        /**
         * Optional. Filter string, adhering to the rules in [List-operation filtering](https://cloud.google.com/secret-manager/docs/filtering). List only secrets matching the filter. If filter is empty, all secrets are listed.
         */
        filter?: string;
        /**
         * Optional. The maximum number of results to be returned in a single page. If set to 0, the server decides the number of results to return. If the number is greater than 25000, it is capped at 25000.
         */
        pageSize?: number;
        /**
         * Optional. Pagination token, returned earlier via ListSecretsResponse.next_page_token.
         */
        pageToken?: string;
        /**
         * Required. The resource name of the project associated with the Secrets, in the format `projects/x`.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Secrets$Patch extends StandardParameters {
        /**
         * Output only. The resource name of the Secret in the format `projects/x/secrets/x`.
         */
        name?: string;
        /**
         * Required. Specifies the fields to be updated.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Secret;
    }
    export interface Params$Resource$Projects$Secrets$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Secrets$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TestIamPermissionsRequest;
    }
    export class Resource$Projects$Secrets$Versions {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Accesses a SecretVersion. This call returns the secret data. `projects/x/secrets/x/versions/latest` is an alias to the most recently created SecretVersion.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        access(params: Params$Resource$Projects$Secrets$Versions$Access, options: StreamMethodOptions): GaxiosPromise<Readable>;
        access(params?: Params$Resource$Projects$Secrets$Versions$Access, options?: MethodOptions): GaxiosPromise<Schema$AccessSecretVersionResponse>;
        access(params: Params$Resource$Projects$Secrets$Versions$Access, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        access(params: Params$Resource$Projects$Secrets$Versions$Access, options: MethodOptions | BodyResponseCallback<Schema$AccessSecretVersionResponse>, callback: BodyResponseCallback<Schema$AccessSecretVersionResponse>): void;
        access(params: Params$Resource$Projects$Secrets$Versions$Access, callback: BodyResponseCallback<Schema$AccessSecretVersionResponse>): void;
        access(callback: BodyResponseCallback<Schema$AccessSecretVersionResponse>): void;
        /**
         * Destroys a SecretVersion. Sets the state of the SecretVersion to DESTROYED and irrevocably destroys the secret data.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        destroy(params: Params$Resource$Projects$Secrets$Versions$Destroy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        destroy(params?: Params$Resource$Projects$Secrets$Versions$Destroy, options?: MethodOptions): GaxiosPromise<Schema$SecretVersion>;
        destroy(params: Params$Resource$Projects$Secrets$Versions$Destroy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        destroy(params: Params$Resource$Projects$Secrets$Versions$Destroy, options: MethodOptions | BodyResponseCallback<Schema$SecretVersion>, callback: BodyResponseCallback<Schema$SecretVersion>): void;
        destroy(params: Params$Resource$Projects$Secrets$Versions$Destroy, callback: BodyResponseCallback<Schema$SecretVersion>): void;
        destroy(callback: BodyResponseCallback<Schema$SecretVersion>): void;
        /**
         * Disables a SecretVersion. Sets the state of the SecretVersion to DISABLED.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        disable(params: Params$Resource$Projects$Secrets$Versions$Disable, options: StreamMethodOptions): GaxiosPromise<Readable>;
        disable(params?: Params$Resource$Projects$Secrets$Versions$Disable, options?: MethodOptions): GaxiosPromise<Schema$SecretVersion>;
        disable(params: Params$Resource$Projects$Secrets$Versions$Disable, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        disable(params: Params$Resource$Projects$Secrets$Versions$Disable, options: MethodOptions | BodyResponseCallback<Schema$SecretVersion>, callback: BodyResponseCallback<Schema$SecretVersion>): void;
        disable(params: Params$Resource$Projects$Secrets$Versions$Disable, callback: BodyResponseCallback<Schema$SecretVersion>): void;
        disable(callback: BodyResponseCallback<Schema$SecretVersion>): void;
        /**
         * Enables a SecretVersion. Sets the state of the SecretVersion to ENABLED.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        enable(params: Params$Resource$Projects$Secrets$Versions$Enable, options: StreamMethodOptions): GaxiosPromise<Readable>;
        enable(params?: Params$Resource$Projects$Secrets$Versions$Enable, options?: MethodOptions): GaxiosPromise<Schema$SecretVersion>;
        enable(params: Params$Resource$Projects$Secrets$Versions$Enable, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        enable(params: Params$Resource$Projects$Secrets$Versions$Enable, options: MethodOptions | BodyResponseCallback<Schema$SecretVersion>, callback: BodyResponseCallback<Schema$SecretVersion>): void;
        enable(params: Params$Resource$Projects$Secrets$Versions$Enable, callback: BodyResponseCallback<Schema$SecretVersion>): void;
        enable(callback: BodyResponseCallback<Schema$SecretVersion>): void;
        /**
         * Gets metadata for a SecretVersion. `projects/x/secrets/x/versions/latest` is an alias to the most recently created SecretVersion.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Secrets$Versions$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Secrets$Versions$Get, options?: MethodOptions): GaxiosPromise<Schema$SecretVersion>;
        get(params: Params$Resource$Projects$Secrets$Versions$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Secrets$Versions$Get, options: MethodOptions | BodyResponseCallback<Schema$SecretVersion>, callback: BodyResponseCallback<Schema$SecretVersion>): void;
        get(params: Params$Resource$Projects$Secrets$Versions$Get, callback: BodyResponseCallback<Schema$SecretVersion>): void;
        get(callback: BodyResponseCallback<Schema$SecretVersion>): void;
        /**
         * Lists SecretVersions. This call does not return secret data.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Secrets$Versions$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Secrets$Versions$List, options?: MethodOptions): GaxiosPromise<Schema$ListSecretVersionsResponse>;
        list(params: Params$Resource$Projects$Secrets$Versions$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Secrets$Versions$List, options: MethodOptions | BodyResponseCallback<Schema$ListSecretVersionsResponse>, callback: BodyResponseCallback<Schema$ListSecretVersionsResponse>): void;
        list(params: Params$Resource$Projects$Secrets$Versions$List, callback: BodyResponseCallback<Schema$ListSecretVersionsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListSecretVersionsResponse>): void;
    }
    export interface Params$Resource$Projects$Secrets$Versions$Access extends StandardParameters {
        /**
         * Required. The resource name of the SecretVersion in the format `projects/x/secrets/x/versions/x`. `projects/x/secrets/x/versions/latest` is an alias to the most recently created SecretVersion.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Secrets$Versions$Destroy extends StandardParameters {
        /**
         * Required. The resource name of the SecretVersion to destroy in the format `projects/x/secrets/x/versions/x`.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DestroySecretVersionRequest;
    }
    export interface Params$Resource$Projects$Secrets$Versions$Disable extends StandardParameters {
        /**
         * Required. The resource name of the SecretVersion to disable in the format `projects/x/secrets/x/versions/x`.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DisableSecretVersionRequest;
    }
    export interface Params$Resource$Projects$Secrets$Versions$Enable extends StandardParameters {
        /**
         * Required. The resource name of the SecretVersion to enable in the format `projects/x/secrets/x/versions/x`.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$EnableSecretVersionRequest;
    }
    export interface Params$Resource$Projects$Secrets$Versions$Get extends StandardParameters {
        /**
         * Required. The resource name of the SecretVersion in the format `projects/x/secrets/x/versions/x`. `projects/x/secrets/x/versions/latest` is an alias to the most recently created SecretVersion.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Secrets$Versions$List extends StandardParameters {
        /**
         * Optional. Filter string, adhering to the rules in [List-operation filtering](https://cloud.google.com/secret-manager/docs/filtering). List only secret versions matching the filter. If filter is empty, all secret versions are listed.
         */
        filter?: string;
        /**
         * Optional. The maximum number of results to be returned in a single page. If set to 0, the server decides the number of results to return. If the number is greater than 25000, it is capped at 25000.
         */
        pageSize?: number;
        /**
         * Optional. Pagination token, returned earlier via ListSecretVersionsResponse.next_page_token][].
         */
        pageToken?: string;
        /**
         * Required. The resource name of the Secret associated with the SecretVersions to list, in the format `projects/x/secrets/x`.
         */
        parent?: string;
    }
    export {};
}
