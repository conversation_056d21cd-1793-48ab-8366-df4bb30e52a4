const express = require('express');
const path = require('path');

const app = express();
const PORT = 3000;

// Serve static files
app.use(express.static('public'));

// Main route
app.get('/', (req, res) => {
  res.redirect('/demo.html');
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    name: 'Praatika Email Agent',
    status: 'running',
    mode: 'demo',
    timestamp: new Date().toISOString()
  });
});

app.listen(PORT, () => {
  console.log(`🤖 Praatika Agent running on http://localhost:${PORT}`);
  console.log('📊 Demo Dashboard: http://localhost:3000/demo.html');
  console.log('🔧 Status: Demo Mode (add Google Client Secret for full functionality)');
});
