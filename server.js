const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const cron = require('node-cron');
const fs = require('fs');
const path = require('path');

// Import configuration and utilities
const config = require('./config/config');
const logger = require('./utils/logger');

// Import services
const OAuthHandler = require('./auth/oauthHandler');
const EmailService = require('./services/emailService');
const AIService = require('./services/aiService');
const SheetsService = require('./services/sheetsService');
const EmailProcessor = require('./utils/emailProcessor');

// Import routes
const createAuthRoutes = require('./routes/auth');
const createApiRoutes = require('./routes/api');
const createWebhookRoutes = require('./routes/webhook');

class PraatikaAgent {
  constructor() {
    this.app = express();
    this.oauthHandler = new OAuthHandler();
    this.emailService = new EmailService(this.oauthHandler);
    this.aiService = new AIService();
    this.sheetsService = new SheetsService(this.oauthHandler);
    this.emailProcessor = new EmailProcessor(this.aiService, this.sheetsService);

    this.isProcessing = false;
    this.cronJob = null;
  }

  /**
   * Initialize the application
   */
  async initialize() {
    try {
      // Create logs directory if it doesn't exist
      if (!fs.existsSync('logs')) {
        fs.mkdirSync('logs');
      }

      // Setup middleware
      this.setupMiddleware();

      // Setup routes
      this.setupRoutes();

      // Setup error handling
      this.setupErrorHandling();

      // Start the server
      this.startServer();

      // Setup email monitoring
      this.setupEmailMonitoring();

      logger.info('Praatika Agent initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Praatika Agent:', error);
      process.exit(1);
    }
  }

  /**
   * Setup Express middleware
   */
  setupMiddleware() {
    // Security middleware
    this.app.use(helmet());

    // CORS
    this.app.use(cors({
      origin: process.env.NODE_ENV === 'production' ? false : true,
      credentials: true
    }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
      message: 'Too many requests from this IP'
    });
    this.app.use(limiter);

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));

    // Serve static files
    this.app.use(express.static('public'));

    // Request logging
    this.app.use((req, res, next) => {
      logger.info(`${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      next();
    });
  }

  /**
   * Setup application routes
   */
  setupRoutes() {
    // Health check and main route
    this.app.get('/', (req, res) => {
      // Check if we have the minimum required configuration
      const hasClientSecret = config.google.clientSecret && !config.google.clientSecret.includes('PLACEHOLDER');

      if (!hasClientSecret) {
        // Redirect to demo mode if not fully configured
        res.redirect('/demo.html');
        return;
      }

      // Serve the main dashboard if configured
      res.sendFile('index.html', { root: 'public' });
    });

    // API status endpoint
    this.app.get('/api/status', (req, res) => {
      res.json({
        name: 'Praatika Email Agent',
        version: '1.0.0',
        status: 'running',
        authenticated: this.oauthHandler.isAuthenticated(),
        timestamp: new Date().toISOString()
      });
    });

    // Authentication routes
    this.app.use('/auth', createAuthRoutes(this.oauthHandler));

    // API routes
    this.app.use('/api', createApiRoutes(
      this.emailService,
      this.emailProcessor,
      this.sheetsService
    ));

    // Webhook routes
    this.app.use('/webhook', createWebhookRoutes(
      this.emailService,
      this.emailProcessor
    ));
  }

  /**
   * Setup error handling
   */
  setupErrorHandling() {
    // 404 handler
    this.app.use((req, res) => {
      res.status(404).json({
        error: 'Not Found',
        message: 'The requested endpoint does not exist'
      });
    });

    // Global error handler
    this.app.use((error, req, res, next) => {
      logger.error('Unhandled error:', error);

      res.status(500).json({
        error: 'Internal Server Error',
        message: config.server.env === 'development' ? error.message : 'Something went wrong'
      });
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error);
      process.exit(1);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      process.exit(1);
    });
  }

  /**
   * Start the Express server
   */
  startServer() {
    this.app.listen(config.server.port, () => {
      logger.info(`Praatika Agent running on port ${config.server.port}`);
      logger.info(`Environment: ${config.server.env}`);
      logger.info('Available endpoints:');
      logger.info('  GET  / - Status and info');
      logger.info('  GET  /auth/google - Start OAuth flow');
      logger.info('  GET  /auth/status - Check authentication');
      logger.info('  GET  /api/health - Health check');
      logger.info('  GET  /api/emails/check - Manual email check');
      logger.info('  GET  /api/stats - Processing statistics');
    });
  }

  /**
   * Setup automated email monitoring
   */
  setupEmailMonitoring() {
    // Schedule email checking every minute
    this.cronJob = cron.schedule('* * * * *', async () => {
      if (!this.oauthHandler.isAuthenticated()) {
        logger.debug('Skipping email check - not authenticated');
        return;
      }

      if (this.isProcessing) {
        logger.debug('Skipping email check - already processing');
        return;
      }

      try {
        this.isProcessing = true;
        logger.debug('Starting scheduled email check');

        const emails = await this.emailService.fetchNewEmails();

        if (emails.length > 0) {
          const results = await this.emailProcessor.processBatch(emails);
          logger.info(`Scheduled processing completed: ${results.processed} emails processed`);
        }
      } catch (error) {
        logger.error('Error in scheduled email processing:', error);
      } finally {
        this.isProcessing = false;
      }
    });

    logger.info('Email monitoring scheduled (every minute)');
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    logger.info('Shutting down Praatika Agent...');

    if (this.cronJob) {
      this.cronJob.destroy();
      logger.info('Stopped email monitoring');
    }

    // Stop Gmail push notifications if active
    try {
      await this.emailService.stopPushNotifications();
    } catch (error) {
      logger.warn('Error stopping push notifications:', error);
    }

    logger.info('Praatika Agent shutdown complete');
    process.exit(0);
  }
}

// Handle shutdown signals
process.on('SIGTERM', () => {
  logger.info('SIGTERM received');
  if (global.praatikaAgent) {
    global.praatikaAgent.shutdown();
  }
});

process.on('SIGINT', () => {
  logger.info('SIGINT received');
  if (global.praatikaAgent) {
    global.praatikaAgent.shutdown();
  }
});

// Start the application
const praatikaAgent = new PraatikaAgent();
global.praatikaAgent = praatikaAgent;
praatikaAgent.initialize();
